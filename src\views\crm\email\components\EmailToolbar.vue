<template>
  <div class="email-toolbar">
    <div class="toolbar-group">
      <!-- 回复按钮组 -->
      <div class="split-button-group">
        <button class="action-button main-button" @click="handleReply('normal')">
          <reply-icon class="icon-small" /> 回复
        </button>
        <div class="dropdown-wrapper" @click.stop>
          <button
            class="action-button dropdown-trigger"
            @click="toggleReplyDropdown"
            :class="{ 'active': showReplyDropdown }"
          >
            <chevron-down-icon class="icon-tiny" />
          </button>
          <div
            v-if="showReplyDropdown"
            class="dropdown-menu"
            @click.stop
          >
            <div class="dropdown-item" @click="handleReply('with-attachment')">
              <paperclip-icon class="icon-tiny" />
              回复（带附件）
            </div>
            <div class="dropdown-item" @click="handleReply('without-original')">
              <file-text-icon class="icon-tiny" />
              回复（不带原文）
            </div>
          </div>
        </div>
      </div>

      <!-- 回复全部按钮组 -->
      <div class="split-button-group">
        <button class="action-button main-button" @click="handleReplyAll('normal')">
          <reply-all-icon class="icon-small" /> 回复全部
        </button>
        <div class="dropdown-wrapper" @click.stop>
          <button
            class="action-button dropdown-trigger"
            @click="toggleReplyAllDropdown"
            :class="{ 'active': showReplyAllDropdown }"
          >
            <chevron-down-icon class="icon-tiny" />
          </button>
          <div
            v-if="showReplyAllDropdown"
            class="dropdown-menu"
            @click.stop
          >
            <div class="dropdown-item" @click="handleReplyAll('with-attachment')">
              <paperclip-icon class="icon-tiny" />
              回复全部（带附件）
            </div>
            <div class="dropdown-item" @click="handleReplyAll('without-original')">
              <file-text-icon class="icon-tiny" />
              回复全部（不带原文）
            </div>
          </div>
        </div>
      </div>

      <!-- 转发按钮 -->
      <button class="action-button" @click="$emit('forward', email)">
        <forward-icon class="icon-small" /> 转发
      </button>

      <!-- 归档按钮 -->
      <button class="action-button" @click="$emit('archive', email)">
        <archive-icon class="icon-small" /> 归档
      </button>

      <!-- 标签按钮 -->
      <button class="action-button" @click="$emit('tag', email)">
        <tag-icon class="icon-small" /> 标签
      </button>

      <!-- 删除按钮及下拉菜单 -->
      <dropdown-menu>
        <template #trigger>
          <button class="action-button more-button">
            <trash-2-icon class="icon-small" /> 删除 <chevron-down-icon class="icon-tiny more-chevron" />
          </button>
        </template>

        <dropdown-item @click="confirmDelete('normal')">
          <template #icon>
            <trash-2-icon class="icon-tiny" />
          </template>
          删除
        </dropdown-item>

        <dropdown-item @click="confirmDelete('permanent')" is-danger>
          <template #icon>
            <trash-icon class="icon-tiny" />
          </template>
          彻底删除
        </dropdown-item>

        <dropdown-item @click="confirmDelete('trash')">
          <template #icon>
            <archive-icon class="icon-tiny" />
          </template>
          移入垃圾箱
        </dropdown-item>
      </dropdown-menu>

      <!-- 翻译按钮 -->
      <button class="action-button" @click="$emit('translate')">
        <Languages-icon class="icon-small" /> 翻译
      </button>

      <!-- 星标按钮 -->
      <button class="action-button" @click="$emit('star', email)">
        <star-icon class="icon-small" :class="{ 'starred': email.starred }" />
        {{ email.starred ? '取消星标' : '星标置顶' }}
      </button>

            <!-- 更多按钮及下拉菜单 -->
      <dropdown-menu>
        <template #trigger>
          <button class="action-button more-button">
            <message-squareShare-icon class="icon-small" /> 转<chevron-down-icon class="icon-tiny more-chevron" />
          </button>
        </template>

        <dropdown-item @click="addNewclues">
          <template #icon>
            <!-- <mail-icon class="icon-tiny" /> -->
          </template>
          新增线索
        </dropdown-item>

        <dropdown-item @click="addSalesorder">
          <template #icon>
            <!-- <mail-x-icon class="icon-tiny" /> -->
          </template>
          新增销售订单
        </dropdown-item>

        <dropdown-item @click="relatedDocuments">
          <template #icon>
            <!-- <mail-check-icon class="icon-tiny" /> -->
          </template>
          关联单据
        </dropdown-item>
      </dropdown-menu>

      <!-- 更多按钮及下拉菜单 -->
      <dropdown-menu>
        <template #trigger>
          <button class="action-button more-button">
            <ellipsis-vertical-Icon class="icon-small" /> 更多 <chevron-down-icon class="icon-tiny more-chevron" />
          </button>
        </template>

        <dropdown-item @click="markAsUnread">
          <template #icon>
            <mail-icon class="icon-tiny" />
          </template>
          标记为未读
        </dropdown-item>

        <dropdown-item @click="markAsNoReply">
          <template #icon>
            <mail-x-icon class="icon-tiny" />
          </template>
          标记为未回复
        </dropdown-item>

        <dropdown-item @click="markAsForwarded">
          <template #icon>
            <mail-check-icon class="icon-tiny" />
          </template>
          标记为已转发
        </dropdown-item>

        <dropdown-item @click="sendAsAttachment">
          <template #icon>
            <paperclip-icon class="icon-tiny" />
          </template>
          作为附件发送
        </dropdown-item>

        <dropdown-item @click="exportEml">
          <template #icon>
            <file-output-icon class="icon-tiny" />
          </template>
          导出eml文件
        </dropdown-item>

        <dropdown-item @click="viewNewTab">
          <template #icon>
            <book-open-icon class="icon-tiny" />
          </template>
          新标签页查看
        </dropdown-item>

        <dropdown-item @click="editSubjectInline">
          <template #icon>
            <pen-line-icon class="icon-tiny" />
          </template>
          修改主题
        </dropdown-item>

        <dropdown-item @click="setReminderInline">
          <template #icon>
            <calendar-clock-icon class="icon-tiny" />
          </template>
          设置提醒
        </dropdown-item>

        <dropdown-item @click="printEmailInline">
          <template #icon>
            <printer-icon class="icon-tiny" />
          </template>
          打印邮件
        </dropdown-item>

        <dropdown-item @click="viewLog">
          <template #icon>
            <file-text-icon class="icon-tiny" />
          </template>
          内分发日志
        </dropdown-item>

        <dropdown-item @click="showCorrections">
          <template #icon>
            <diff-icon class="icon-tiny" />
          </template>
          显示修正
        </dropdown-item>

        <dropdown-item @click="holdEmail">
          <template #icon>
            <clipboard-icon class="icon-tiny" />
          </template>
          挂报邮件
        </dropdown-item>
      </dropdown-menu>
    </div>

    <div class="toolbar-group">
      <!-- 上一封/下一封邮件导航 -->
      <button
        class="toolbar-btn"
        :disabled="!hasPrevEmail"
        @click="navigateToPrevEmail"
      >
        <chevron-left-icon class="icon" />
      </button>

      <button
        class="toolbar-btn"
        :disabled="!hasNextEmail"
        @click="navigateToNextEmail"
      >
        <chevron-right-icon class="icon" />
      </button>

      <!-- 全屏查看按钮 -->
      <button class="toolbar-btn" @click="openFullscreen">
        <maximize-icon class="icon" />
      </button>

      <!-- 临时测试按钮 -->
      <button class="toolbar-btn test-btn" @click="addNewclues" style="background: #e60012; color: white; margin-left: 8px;">
        测试新增线索
      </button>
    </div>

    <!-- 确认对话框 -->
    <confirm-dialog
      :visible="showConfirmDialog"
      :title="confirmDialogTitle"
      :message="confirmDialogMessage"
      :is-danger="confirmDialogType === 'permanent'"
      @confirm="handleConfirmAction"
      @cancel="showConfirmDialog = false"
    />

    <!-- 修改主题对话框 -->
    <confirm-dialog
      v-if="showEditSubjectDialog"
      :visible="showEditSubjectDialog"
      title="修改邮件主题"
      :message="''"
      confirm-text="保存"
      @confirm="saveSubject"
      @cancel="cancelEditSubject"
    >
      <div class="edit-subject-container">
        <input
          type="text"
          v-model="editedSubject"
          class="subject-input"
          placeholder="请输入新的邮件主题"
        />
      </div>
    </confirm-dialog>

    <!-- 提醒设置对话框 -->
    <reminder-dialog
      :visible="showReminderDialog"
      :email-subject="email.subject"
      @confirm="handleSetReminder"
      @cancel="showReminderDialog = false"
    />
  </div>
</template>

<script>
import {
  Reply,
  ReplyAll,
  Forward,
  Archive,
  Tag,
  Trash2,
  Trash,
  Languages,
  Star,
  MoreHorizontal,
  ChevronLeft,
  ChevronRight,
  ChevronDown,
  Maximize,
  Edit,
  Bell,
  Printer,
  EllipsisVertical,
  Mail,
  MailCheck,
  MailX,
  Paperclip,
  FileOutput,
  BookOpen,
  PenLine,
  Clock,
  CalendarClock,
  FileText,
  Diff,
  Clipboard,
  MessageSquareShare
} from 'lucide-vue'

import DropdownMenu from './DropdownMenu.vue'
import DropdownItem from './DropdownItem.vue'
import ConfirmDialog from './ConfirmDialog.vue'
import ReminderDialog from './ReminderDialog.vue'

export default {
  name: 'EmailToolbar',
  components: {
    // 使用与 index.vue 相同的图标注册方式
    ReplyIcon: Reply,
    ReplyAllIcon: ReplyAll,
    ForwardIcon: Forward,
    ArchiveIcon: Archive,
    TagIcon: Tag,
    Trash2Icon: Trash2,
    TrashIcon: Trash,
    LanguagesIcon: Languages,
    StarIcon: Star,
    MoreHorizontalIcon: MoreHorizontal,
    ChevronLeftIcon: ChevronLeft,
    ChevronRightIcon: ChevronRight,
    ChevronDownIcon: ChevronDown,
    MaximizeIcon: Maximize,
    EditIcon: Edit,
    BellIcon: Bell,
    PrinterIcon: Printer,
    MailIcon: Mail,
    MailCheckIcon: MailCheck,
    MailXIcon: MailX,
    PaperclipIcon: Paperclip,
    FileOutputIcon: FileOutput,
    BookOpenIcon: BookOpen,
    PenLineIcon: PenLine,
    ClockIcon: Clock,
    CalendarClockIcon: CalendarClock,
    FileTextIcon: FileText,
    DiffIcon: Diff,
    ClipboardIcon: Clipboard,
    DropdownMenu,
    DropdownItem,
    ConfirmDialog,
    ReminderDialog,
    EllipsisVerticalIcon: EllipsisVertical,
    MessageSquareShareIcon: MessageSquareShare
  },
  props: {
    email: {
      type: Object,
      required: true
    },
    hasPrevEmail: {
      type: Boolean,
      default: false
    },
    hasNextEmail: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showConfirmDialog: false,
      confirmDialogTitle: '确认删除',
      confirmDialogMessage: '',
      confirmDialogType: 'normal',

      showEditSubjectDialog: false,
      editedSubject: '',

      showReminderDialog: false,

      // 回复下拉菜单状态
      showReplyDropdown: false,
      showReplyAllDropdown: false
    }
  },
  mounted() {
    // 添加全局点击事件监听器，用于关闭下拉菜单
    document.addEventListener('click', this.handleGlobalClick);
  },
  beforeDestroy() {
    // 移除全局点击事件监听器
    document.removeEventListener('click', this.handleGlobalClick);
  },
  methods: {
    // 处理全局点击事件
    handleGlobalClick(event) {
      // 检查点击是否在下拉菜单外部
      const target = event.target;
      const isInsideDropdown = target.closest('.split-button-group');

      if (!isInsideDropdown) {
        this.showReplyDropdown = false;
        this.showReplyAllDropdown = false;
      }
    },
    // 回复相关方法
    handleReply(type) {
      this.showReplyDropdown = false;
      this.$emit('reply', { email: this.email, type });
    },

    handleReplyAll(type) {
      this.showReplyAllDropdown = false;
      this.$emit('reply-all', { email: this.email, type });
    },

    toggleReplyDropdown() {
      this.showReplyDropdown = !this.showReplyDropdown;
      // 关闭其他下拉菜单
      this.showReplyAllDropdown = false;
    },

    toggleReplyAllDropdown() {
      this.showReplyAllDropdown = !this.showReplyAllDropdown;
      // 关闭其他下拉菜单
      this.showReplyDropdown = false;
    },

    confirmDelete(type) {
      this.confirmDialogType = type;

      switch(type) {
        case 'normal':
          this.confirmDialogTitle = '确认删除';
          this.confirmDialogMessage = '您选择了1封邮件，确定要删除吗？';
          break;
        case 'permanent':
          this.confirmDialogTitle = '确认彻底删除';
          this.confirmDialogMessage = '邮件删除后将无法恢复，确定要彻底删除吗？';
          break;
        case 'trash':
          this.confirmDialogTitle = '确认移入垃圾箱';
          this.confirmDialogMessage = '您确定要将该邮件移入垃圾箱吗？';
          break;
      }

      this.showConfirmDialog = true;
    },

    handleConfirmAction() {
      this.showConfirmDialog = false;

      switch(this.confirmDialogType) {
        case 'normal':
          this.$emit('delete', { email: this.email, type: 'normal' });
          break;
        case 'permanent':
          this.$emit('delete', { email: this.email, type: 'permanent' });
          break;
        case 'trash':
          this.$emit('delete', { email: this.email, type: 'trash' });
          break;
      }
    },

    editSubject() {
      this.editedSubject = this.email.subject;
      this.showEditSubjectDialog = true;
    },

    saveSubject() {
      if (this.editedSubject.trim()) {
        this.$emit('edit-subject', {
          email: this.email,
          newSubject: this.editedSubject.trim()
        });
      }
      this.showEditSubjectDialog = false;
    },

    cancelEditSubject() {
      this.showEditSubjectDialog = false;
    },

    handleSetReminder(reminderData) {
      this.$emit('set-reminder', {
        email: this.email,
        reminder: reminderData
      });
      this.showReminderDialog = false;
    },

    printEmail() {
      window.print();
    },

    navigateToPrevEmail() {
      if (this.hasPrevEmail) {
        this.$emit('navigate', 'prev');
      }
    },

    navigateToNextEmail() {
      if (this.hasNextEmail) {
        this.$emit('navigate', 'next');
      }
    },

    openFullscreen() {
      // 发送事件到父组件
      this.$emit('fullscreen', this.email);

      // 直接使用全局事件总线添加新标签页，并标记为全屏模式
      this.$bus.emit('add-tab', {
        id: `fullscreen-${this.email.id}`,
        title: `预览: ${this.email.subject.substring(0, 10)}${this.email.subject.length > 10 ? '...' : ''}`,
        type: 'fullscreen',
        email: this.email,
        closable: true,
        isFullscreen: true, // 标记为全屏模式
        keepNavbar: true // 保留顶部导航栏
      });

      // 添加一个短暂延迟，确保DOM更新后再添加样式
      setTimeout(() => {
        const container = document.querySelector('.tab-content-container');
        if (container) {
          container.classList.add('fullscreen-mode');
        }
      }, 100);
    },

    // 新增的菜单项方法
    addNewclues() {
      console.log('🔥 addNewclues 方法被调用', this.email);

      this.$emit('add-newclues', this.email);

      // 直接使用全局事件总线添加新标签页
      const tabData = {
        id: `clue-${this.email.id}`,
        title: `新增线索: ${this.email.subject.substring(0, 10)}${this.email.subject.length > 10 ? '...' : ''}`,
        type: 'clue',
        email: this.email,
        closable: true
      };

      console.log('🔥 准备发送 add-tab 事件', tabData);
      this.$bus.emit('add-tab', tabData);

      // 添加一个延迟检查，确保事件被处理
      setTimeout(() => {
        console.log('🔥 检查标签页是否已添加');
      }, 100);
    },
    addSalesorder() {
      this.$emit('add-salesorder', this.email);
      // 直接使用全局事件总线添加新标签页
      this.$bus.emit('add-tab', {
        id: `order-${this.email.id}`,
        title: `新增订单: ${this.email.subject.substring(0, 10)}${this.email.subject.length > 10 ? '...' : ''}`,
        type: 'order',
        email: this.email,
        closable: true
      });
    },
    relatedDocuments() {
      this.$emit('related-documents', this.email);
    },

    // 新增的更多菜单项方法
    markAsUnread() {
      this.$emit('mark-unread', this.email);
    },

    markAsNoReply() {
      this.$emit('mark-no-reply', this.email);
    },

    markAsForwarded() {
      this.$emit('mark-forwarded', this.email);
    },

    sendAsAttachment() {
      this.$emit('send-as-attachment', this.email);
    },

    exportEml() {
      this.$emit('export-eml', this.email);
    },

    viewNewTab() {
      this.$emit('view-new-tab', this.email);
      // 直接使用全局事件总线添加新标签页
      this.$bus.emit('add-tab', {
        id: `view-${this.email.id}`,
        title: `查看: ${this.email.subject.substring(0, 10)}${this.email.subject.length > 10 ? '...' : ''}`,
        type: 'fullscreen',
        email: this.email,
        closable: true,
        isFullscreen: true, // 标记为全屏模式
        keepNavbar: true // 保留顶部导航栏
      });

      // 添加一个短暂延迟，确保DOM更新后再添加样式
      setTimeout(() => {
        const container = document.querySelector('.tab-content-container');
        if (container) {
          container.classList.add('fullscreen-mode');
        }
      }, 100);
    },

    editSubjectInline() {
      this.editSubject(); // 复用现有的修改主题方法
    },

    setReminderInline() {
      this.showReminderDialog = true; // 复用现有的设置提醒方法
    },

    printEmailInline() {
      this.printEmail(); // 复用现有的打印方法
    },

    viewLog() {
      this.$emit('view-log', this.email);
    },

    showCorrections() {
      this.$emit('show-corrections', this.email);
    },

    holdEmail() {
      this.$emit('hold-email', this.email);
    }
  }
}
</script>

<style scoped>
.email-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* border-bottom: 1px solid #ccc; */
  margin-bottom: 16px;
  padding-bottom: 12px;
}

.toolbar-group {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.toolbar-group:first-child {
  flex: 1;
}

.action-button {
  padding: 6px 12px;
  background-color: #f5f7fa;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  margin-right: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  font-size: 14px;
  transition: all 0.2s;
  color: #333 !important; /* 强制设置按钮字体和SVG颜色 */
}

/* 修正 ::v-deep 用法，确保 SVG 样式生效 */
:deep(.action-button svg),
:deep(.toolbar-btn svg) {
  width: 16px;
  height: 16px;
  display: inline-block;
  flex-shrink: 0;
  color: inherit;
  margin-right: 4px;
}

.action-button:hover {
  background-color: #e6e9ed;
}

.toolbar-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  color: #666 !important; /* 强制设置按钮字体和SVG颜色 */
  margin-left: 4px;
  transition: all 0.2s;
  padding: 0;
}

:deep(.toolbar-btn svg) {
  margin-right: 0 !important;
}

.toolbar-btn:hover:not(:disabled) {
  background-color: #f5f7fa;
}

.toolbar-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.icon-small {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  display: inline-block;
  flex-shrink: 0;
  color: inherit;
}

.icon-tiny {
  width: 14px;
  height: 14px;
  margin-right: 4px;
  display: inline-block;
  flex-shrink: 0;
  color: inherit;
}

.icon {
  width: 18px;
  height: 18px;
  display: inline-block;
  flex-shrink: 0;
  color: inherit;
}

.starred {
  color: #f5a623;
  fill: #f5a623;
}

.more-button {
  display: flex;
  align-items: center;
}

.more-chevron {
  margin-left: 4px;
  margin-right: 0;
  transition: transform 0.2s;
}

.dropdown-container.active .more-chevron {
  transform: rotate(180deg);
}

.edit-subject-container {
  margin-top: 8px;
}

.subject-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
}

/* 分离式按钮组样式 */
.split-button-group {
  display: flex;
  position: relative;
  margin-right: 8px;
  margin-bottom: 8px;
}

.split-button-group .main-button {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
  margin-right: 0;
  margin-bottom: 0;
}

.split-button-group .dropdown-trigger {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: 1px solid #d9d9d9;
  margin-right: 0;
  margin-bottom: 0;
  padding: 6px 8px;
  min-width: auto;
}

.split-button-group .dropdown-trigger:hover,
.split-button-group .dropdown-trigger.active {
  background-color: #e6e9ed;
}

.dropdown-wrapper {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 180px;
  margin-top: 2px;
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
  font-size: 14px;
  color: #333;
}

.dropdown-item:hover {
  background-color: #f5f7fa;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item .icon-tiny {
  margin-right: 8px;
  color: #666;
}

@media (max-width: 768px) {
  .email-toolbar {
    flex-direction: column;
    gap: 8px;
  }

  .toolbar-group {
    flex-wrap: wrap;
  }

  .split-button-group {
    margin-right: 4px;
    margin-bottom: 4px;
  }

  .dropdown-menu {
    min-width: 160px;
  }
}
</style>

<style>
/* 全局样式，确保所有 SVG 图标都能显示 */
.action-button svg,
.toolbar-btn svg,
.icon-small svg,
.icon-tiny svg,
.icon {
  width: 16px;
  height: 16px;
  display: inline-block;
  flex-shrink: 0;
  color: #333 !important;
}

.starred svg {
  color: #f5a623 !important;
  fill: #f5a623 !important;
}
</style>
