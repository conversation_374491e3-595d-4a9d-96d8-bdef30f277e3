<template>
  <div class="email-app">
    <!-- 主视图 -->
    <div v-if="currentView === 'inbox'" class="email-inbox">
      <!-- 左侧边栏 -->
      <div class="sidebar" v-if="activeTab === 'email'">
        <div class="email-tabs">
          <div class="tab active" @click="switchTab('email')">邮件</div>
          <div class="tab" @click="switchTab('customer')">客户邮件</div>
        </div>
        <div class="user-info">
          <div class="avatar">
            <img src="./assets/avatar.png" alt="用户头像" />
          </div>
          <span class="username">钟秋玲 <chevron-down-icon class="icon-small" /></span>
        </div>
        <button class="compose-btn" @click="openCompose">
          写邮件 <chevron-down-icon class="icon-small" />
        </button>

        <div class="sidebar-section">
          <div class="section-header" @click="toggleSection('common')">
            <chevron-right-icon v-if="!sections.common" class="icon-small" />
            <chevron-down-icon v-else class="icon-small" />
            常用功能
          </div>
          <div v-if="sections.common" class="section-content">
            <div
              class="sidebar-item"
              :class="{ active: activeFilter === 'unread' }"
              @click="filterBySpecial('unread')"
            >
              <mail-icon class="icon-small" />
              全部未分发邮件
              <span class="count">{{ getUnreadCount() }}</span>
            </div>
            <div
              class="sidebar-item"
              :class="{ active: activeFilter === 'starred' }"
              @click="filterBySpecial('starred')"
            >
              <star-icon class="icon-small" />
              星标邮件
              <span class="count">{{ getStarredCount() }}</span>
            </div>
            <div class="sidebar-item">
              <users-icon class="icon-small" />
              一对一邮件
            </div>
            <div class="sidebar-item">
              <clock-icon class="icon-small" />
              发件追踪
            </div>
            <div class="sidebar-item">
              <list-icon class="icon-small" />
              待办审批列表
            </div>
          </div>
        </div>
        <div class="sidebar-section">
          <div class="section-header" @click="toggleSection('account')">
            <chevron-right-icon v-if="!sections.account" class="icon-small" />
            <chevron-down-icon v-else class="icon-small" />
            我的全部账号 <span class="badge">1117</span>
          </div>
          <div v-if="sections.account" class="section-content">
            <div
              class="sidebar-item"
              :class="{ active: activeFilter === 'pending' }"
              @click="filterByPending"
            >
              <folder-icon class="icon-small" />
              待处理 <span class="count">1117 / 1316</span>
            </div>
             <div class="sidebar-item">
              <div class="folder-item" :class="{ active: activeFolder === 'inbox' }" @click="filterByFolder('inbox')">
            <!-- <inbox-icon class="icon-small" /> -->
            收件箱
          </div>
             </div>
             <div class="sidebar-item">
              <div class="folder-item" :class="{ active: activeFolder === 'sent' }" @click="filterByFolder('sent')">
            <!-- <send-icon class="icon-small" /> -->
            发件箱
          </div>
             </div>
             <div class="sidebar-item">
              <div class="folder-item" :class="{ active: activeFolder === 'archive' }" @click="filterByFolder('archive')">
            <!-- <archive-icon class="icon-small" /> -->
            已发件箱
          </div>
             </div>
             <div class="sidebar-item">
              <div class="folder-item" :class="{ active: activeFolder === 'draft' }" @click="filterByFolder('draft')">
            <!-- <file-icon class="icon-small" /> -->
            草稿箱 <span class="count">0 / 33</span>
          </div>
             </div>
             <div class="sidebar-item">
              <div class="folder-item" :class="{ active: activeFolder === 'recycle' }" @click="filterByFolder('recycle')">
            <!-- <reply-icon class="icon-small" /> -->
            回收站
          </div>
             </div>
          <div class="sidebar-item">
            <div class="folder-item" :class="{ active: activeFolder === 'spam' }" @click="filterByFolder('spam')">
                      <!-- <trash-icon class="icon-small" /> -->
                      垃圾邮件箱
                    </div>
          </div>
          </div>
        </div>
<div class="sidebar-section">
          <div class="section-header" @click="toggleSection('query')">
            <chevron-right-icon v-if="!sections.query" class="icon-small" />
            <chevron-down-icon v-else class="icon-small" />
            查询箱
          </div>
          <div v-if="sections.query" class="section-content">
            <div
              class="sidebar-item"
              :class="{ active: activeFilter === 'unread' }"
              @click="filterBySpecial('unread')"
            >
              <mail-icon class="icon-small" />
              所有未读邮件
              <span class="count">{{ getUnreadCount() }}</span>
            </div>
            <div
              class="sidebar-item"
              :class="{ active: activeFilter === 'today' }"
              @click="filterBySpecial('today')"
            >
              <calendar-icon class="icon-small" />
              当日收到的邮件
              <span class="count">{{ getTodayCount() }}</span>
            </div>
            <div
              class="sidebar-item"
              :class="{ active: activeFilter === 'yesterday' }"
              @click="filterBySpecial('yesterday')"
            >
              <calendar-minus-icon class="icon-small" />
              昨日收到的邮件
              <span class="count">{{ getYesterdayCount() }}</span>
            </div>
          </div>
        </div>
        <!-- 标签邮件部分 -->
        <div class="sidebar-section">
          <div class="section-header" @click="toggleSection('tags')">
            <chevron-right-icon v-if="!sections.tags" class="icon-small" />
            <chevron-down-icon v-else class="icon-small" />
            标签邮件
            <button class="add-tag-btn" @click.stop="openTagModal">
              <plus-icon class="icon-small" />
            </button>
          </div>
          <div v-if="sections.tags" class="section-content">
            <!-- 系统分组 -->
            <div class="tag-group">
              <div class="tag-group-header" @click="toggleTagGroup('system')">
                <chevron-right-icon v-if="!tagGroups.system" class="icon-tiny" />
                <chevron-down-icon v-else class="icon-tiny" />
                系统分组
              </div>
              <div v-if="tagGroups.system" class="tag-group-content">
                <div
                  v-for="tagId in [1, 2, 3, 4, 5, 6, 7, 8, 9]"
                  :key="`sys-${tagId}`"
                  class="sidebar-item tag-item"
                  @click="filterByTag(tagId)"
                  :class="{ active: activeTag === tagId }"
                >
                  <div class="tag-color-dot" :style="{ backgroundColor: '#e60012' }"></div>
                  {{ getTagName(tagId) }}
                  <span class="tag-count">{{ getTagCount(tagId) }}</span>
                </div>
              </div>
            </div>

            <!-- 自定义分组 -->
            <div class="tag-group">
              <div class="tag-group-header" @click="toggleTagGroup('custom')">
                <chevron-right-icon v-if="!tagGroups.custom" class="icon-tiny" />
                <chevron-down-icon v-else class="icon-tiny" />
                自定义分组
              </div>
              <div v-if="tagGroups.custom" class="tag-group-content">
                <div
                  v-for="(tag, index) in customTags"
                  :key="index"
                  class="sidebar-item tag-item"
                  @click="filterByTag(tag.id)"
                  :class="{ active: activeTag === tag.id }"
                >
                  <div class="tag-color-dot" :style="{ backgroundColor: tag.color }"></div>
                  {{ tag.name }}
                  <span class="tag-count">{{ getTagCount(tag.id) }}</span>
                  <div class="tag-actions">
                    <edit-icon class="icon-tiny" @click.stop="editTag(tag)" />
                    <trash-2-icon class="icon-tiny" @click.stop="deleteTag(tag)" />
                  </div>
                </div>
                <div v-if="customTags.length === 0" class="no-tags">
                  暂无自定义标签，点击 + 创建
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 归档文件夹部分 -->
        <div class="sidebar-section">
          <div class="section-header" @click="toggleSection('archives')">
            <chevron-right-icon v-if="!sections.archives" class="icon-small" />
            <chevron-down-icon v-else class="icon-small" />
            归档文件夹
            <button class="add-folder-btn" @click.stop="openFolderModal">
              <plus-icon class="icon-small" />
            </button>
          </div>
          <div v-if="sections.archives" class="section-content">
            <div
              v-for="(folder, index) in archiveFolders"
              :key="index"
              class="sidebar-item folder-item"
              @click="filterByArchiveFolder(folder)"
              :class="{ active: activeArchiveFolder === folder.id }"
            >
              <folder-icon class="icon-small" :style="{ color: folder.color }" />
              {{ folder.name }}
              <span class="folder-count">{{ getArchiveFolderCount(folder.id) }}</span>
              <div class="folder-actions">
                <edit-icon class="icon-tiny" @click.stop="editFolder(folder)" />
                <trash-2-icon class="icon-tiny" @click.stop="deleteFolder(folder)" />
              </div>
            </div>
            <div v-if="archiveFolders.length === 0" class="no-folders">
              暂无归档文件夹，点击 + 创建
            </div>
          </div>
        </div>
      </div>

      <!-- 客户邮件组件 -->
      <customer-email
        v-if="activeTab === 'customer'"
        @switch-tab="switchTab"
        @contact-selected="handleContactSelected"
        @customer-selected="handleCustomerSelected"
        @compose-email="handleComposeEmail"
      />

      <!-- 中间邮件列表 -->
      <div class="email-list">
        <div class="email-filter">
          <div class="filter-left">
            {{ currentFilterName }} <chevron-down-icon class="icon-small" />
          </div>
          <div class="search-container">
            <div class="search-box">
              <input
                type="text"
                v-model="searchData.term"
                @input="searchEmails"
                placeholder="请输入主题/收件人/发件人"
              />
              <search-icon class="icon-small search-icon" />
              <x-circle-icon
                v-if="searchData.term"
                class="icon-small clear-icon"
                @click="clearSearch"
              />
            </div>
            <button class="advanced-search-btn" @click="toggleAdvancedSearch">
              <sliders-icon class="icon-small" />
            </button>
          </div>
          <div class="filter-actions" @click="openAdvancedSearchModal">
            <filter-icon class="icon" />
            <!-- <chevron-down-icon class="icon-small" />
            <check-icon class="icon" /> -->
          </div>
        </div>

        <div class="advanced-search" v-if="searchData.showAdvanced">
          <div class="search-filters">
            <div
              class="filter-option"
              :class="{ active: searchData.filter === 'all' }"
              @click="setSearchFilter('all')"
            >
              全部
            </div>
            <div
              class="filter-option"
              :class="{ active: searchData.filter === 'subject' }"
              @click="setSearchFilter('subject')"
            >
              主题
            </div>
            <div
              class="filter-option"
              :class="{ active: searchData.filter === 'sender' }"
              @click="setSearchFilter('sender')"
            >
              发件人
            </div>
            <div
              class="filter-option"
              :class="{ active: searchData.filter === 'content' }"
              @click="setSearchFilter('content')"
            >
              内容
            </div>
          </div>
        </div>

        <div class="email-group">
          <div v-if="searchData.isSearchActive" class="search-results-header">
            搜索结果: {{ filteredEmails.length }} 封邮件
            <button class="clear-search-btn" @click="clearSearch">清除搜索</button>
          </div>
          <div v-else-if="activeTag" class="search-results-header">
            标签: {{ getTagName(activeTag) }} ({{ filteredEmails.length }} 封邮件)
            <button class="clear-search-btn" @click="clearTagFilter">清除筛选</button>
          </div>
          <div v-else-if="activeFilter" class="search-results-header">
            {{ getFilterName(activeFilter) }}: {{ filteredEmails.length }} 封邮件
            <button class="clear-search-btn" @click="clearSpecialFilter">清除筛选</button>
          </div>
          <div v-else-if="activeArchiveFolder" class="search-results-header">
            归档文件夹: {{ getArchiveFolderName(activeArchiveFolder) }} ({{ filteredEmails.length }} 封邮件)
            <button class="clear-search-btn" @click="clearArchiveFilter">清除筛选</button>
          </div>
          <div v-else class="group-header">
            2024年11月 ({{ filteredEmails.length }}封)
          </div>

          <div class="email-item"
            v-for="(email, index) in filteredEmails"
            :key="index"
            @click="selectEmail(email)"
            :class="{ 'unread': !email.read }"
          >
            <div class="email-actions-container">
              <div class="star-container" @click.stop="toggleStarAndTop(email)">
                <star-icon class="icon-small star-icon" :class="{ 'starred': email.starred }" />
              </div>
              <div class="archive-container" @click.stop="showArchiveOptions(email)">
                <archive-icon class="icon-small archive-icon" />
              </div>
            </div>
            <div class="email-sender">
              {{ email.sender }}
              <span class="email-tag" v-if="email.tag">@{{ email.tag }}</span>
            </div>
            <div class="email-time">{{ email.time }}</div>
            <div class="email-subject">
              {{ email.subject }}
              <div class="email-tags" v-if="email.tags && email.tags.length > 0">
                <span
                  v-for="(tagId, tagIndex) in email.tags"
                  :key="tagIndex"
                  class="email-tag-label"
                  :style="{ backgroundColor: getTagColor(tagId) }"
                >
                  {{ getTagName(tagId) }}
                </span>
              </div>
              <div v-if="email.archiveFolder" class="email-archive-folder">
                <folder-icon class="icon-tiny" :style="{ color: getArchiveFolderColor(email.archiveFolder) }" />
                {{ getArchiveFolderName(email.archiveFolder) }}
              </div>
            </div>
            <div class="email-actions" v-if="email.hasAttachment">
              <paperclip-icon class="icon-small" />
              <span v-if="email.attachments">{{ email.attachments.length }}</span>
            </div>
            <div class="email-actions" v-if="email.hasReply">
              <message-square-icon class="icon-small" />
              已读回执
            </div>
          </div>

          <div v-if="filteredEmails.length === 0" class="no-results">
            没有找到匹配的邮件
          </div>
        </div>

        <div class="email-pagination">
          <div class="pagination-info">
            共 {{ filteredEmails.length }} 条
          </div>
          <div class="pagination-size">
            300 条/页 <chevron-down-icon class="icon-small" />
          </div>
          <div class="pagination-nav">
            <chevron-left-icon class="icon-small" />
            1
            <chevron-right-icon class="icon-small" />
          </div>
        </div>
      </div>

      <!-- 右侧邮件内容 -->
      <div class="email-content" >
        <div v-if="selectedEmail">
        <!-- 顶部导航栏 -->

        <div class="email-header">
          <!-- 使用新的邮件工具栏组件 -->
          <email-toolbar
            :email="selectedEmail"
            :has-prev-email="hasPreviousEmail"
            :has-next-email="hasNextEmail"
            @reply="replyEmail"
            @reply-all="replyAllEmail"
            @forward="forwardEmail"
            @archive="showArchiveOptions"
            @tag="openTagModal"
            @translate="toggleLanguageSelector"
            @star="toggleStarAndTop"
            @delete="handleDeleteEmail"
            @edit-subject="handleEditSubject"
            @set-reminder="handleSetReminder"
            @navigate="navigateEmail"
            @fullscreen="openFullscreenView"
            @add-newclues="addNewClue"
            @add-salesorder="addSalesOrder"
            @view-new-tab="viewInNewTab"
          />
          <div class="email-title-container">
            <h2 class="email-title">{{ selectedEmail.subject }}</h2>
            <!-- 邮件标签和归档文件夹信息 -->
            <div class="email-tags-inline">
              <span
                v-for="(tagId, tagIndex) in selectedEmail.tags || []"
                :key="tagIndex"
                class="email-tag-label"
                :style="{ backgroundColor: getTagColor(tagId) }"
              >
                {{ getTagName(tagId) }}
                <x-icon class="icon-tiny" @click="removeTagFromEmail(selectedEmail, tagId)" />
              </span>
              <span
                v-if="selectedEmail.archiveFolder"
                class="email-archive-folder-label"
                :style="{ backgroundColor: getArchiveFolderColor(selectedEmail.archiveFolder) }"
              >
                <folder-icon class="icon-tiny" />
                {{ getArchiveFolderName(selectedEmail.archiveFolder) }}
                <x-icon class="icon-tiny" @click="removeFromArchive(selectedEmail)" />
              </span>
            </div>
          </div>
        </div>

        <div class="email-meta-container">
        <div class="email-meta">
          <div class="sender-info">
            <div class="detail-label">发件人：</div>
            <div class="recipients">
              <span class="recipient">{{ selectedEmail.sender }}</span>
              <span v-if="selectedEmail.tag">@{{ selectedEmail.tag }}</span>
            </div>
          </div>
          <div class="email-date">{{ selectedEmail.fullDate || selectedEmail.time }}</div>
        </div>

        <div class="email-meta">
          <div class="sender-info">
            <div class="detail-label">收件人：</div>
            <div class="recipients">
              <span v-for="(recipient, idx) in selectedEmail.recipients" :key="idx" class="recipient">
                {{ recipient }}
              </span>
            </div>
          </div>
        </div>

        <!-- 抄送人信息 -->
        <div class="email-meta" v-if="selectedEmail.ccRecipients && selectedEmail.ccRecipients.length > 0">
          <div class="sender-info">
            <div class="detail-label">抄送人：</div>
            <div class="recipients">
              <span v-for="(recipient, idx) in selectedEmail.ccRecipients" :key="idx" class="recipient">
                {{ recipient }}
              </span>
              <span v-if="selectedEmail.ccRecipients.length > 3" class="more-recipients" @click="toggleCcExpand">
                {{ showAllCc ? '收起' : `等${selectedEmail.ccRecipients.length - 3}人` }}
              </span>
            </div>
          </div>
        </div>

        <!-- 邮件详情信息 -->
        <div class="email-meta email-details-section">
          <div class="email-details-container">
            <div class="detail-group">
              <div class="detail-item">
                <div class="detail-label">时间：</div>
                <div class="detail-value">{{ formatDateTime(selectedEmail.sendDate) }}</div>
              </div>
               <div class="detail-item">
                <div class="detail-label">接收时间：</div>
                <div class="detail-value">{{ formatDateTime(selectedEmail.receivedDate) }}</div>
              </div>
               <div class="detail-item">
                <div class="detail-label">大小：</div>
                <div class="detail-value">{{ formatFileSize(selectedEmail.size) }}</div>
              </div>
            </div>
            <div class="detail-group">
              <div class="detail-item">
                <div class="detail-label">目录位置：</div>
                <div class="detail-value">{{ selectedEmail.folderName || getActiveFolder() }}</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">归属账号：</div>
                <div class="detail-value">{{ selectedEmail.account }}</div>
              </div>
               <div class="detail-item">
                <div class="detail-label">拥有人：</div>
                <div class="detail-value">{{ selectedEmail.owner }}</div>
              </div>
            </div>
          </div>
        </div>

        </div>

        <!-- AI智能摘要按钮区域 -->
        <div class="ai-summary-section" v-if="!showAiSummary">
          <button
            class="ai-summary-btn"
            @click="generateAiSummary"
            :disabled="isGeneratingSummary"
          >
            <zap-icon class="icon-small" />
            {{ isGeneratingSummary ? '生成中...' : '生成 AI 智能摘要' }}
          </button>
        </div>

        <!-- AI智能摘要结果显示 -->
        <div class="ai-summary-result" v-if="showAiSummary">
          <div class="summary-header">
            <div class="summary-title">
              <zap-icon class="icon-small" />
              邮件智能摘要
            </div>
            <button class="close-summary-btn" @click="closeAiSummary">
              <x-icon class="icon-tiny" />
            </button>
          </div>
          <div class="summary-content">
            <div v-if="isGeneratingSummary" class="summary-loading">
              <rotate-cw-icon class="icon-small spin" /> 正在生成智能摘要...
            </div>
            <div v-else class="summary-text">
              {{ aiSummaryContent }}
            </div>
          </div>
        </div>

        <!-- 语言选择面板 -->
        <div class="language-selector" v-if="showLanguageSelector">
          <div class="language-selector-inline">
            <div class="language-label">
              <Languages-icon class="icon-small" />
            </div>
            <div class="language-selects">
              <select v-model="translationSourceLanguage" class="source-lang">
                <option value="auto">自动检测</option>
                <option v-for="lang in languages" :key="lang.code" :value="lang.code">{{ lang.name }}</option>
              </select>
              <div class="arrow-icon">→</div>
              <select v-model="translationLanguage" class="target-lang">
                <option v-for="lang in languages" :key="lang.code" :value="lang.code">{{ lang.name }}</option>
              </select>
            </div>
            <div class="translation-actions-inline">
              <button class="translate-btn" @click="performTranslation">
                <Languages-icon class="icon-small" /> 翻译
              </button>
              <button class="close-selector-btn" @click="showLanguageSelector = false">
                <x-icon class="icon-tiny" /> 关闭翻译
              </button>
            </div>
          </div>
        </div>



        <!-- 附件显示区域 -->
        <div class="email-attachments" v-if="selectedEmail.attachments && selectedEmail.attachments.length > 0">
          <div class="attachments-header">
            <paperclip-icon class="icon-small" /> 附件 ({{ selectedEmail.attachments.length }})
          </div>
          <div class="attachments-list">
            <div
              v-for="(attachment, index) in selectedEmail.attachments"
              :key="index"
              class="attachment-item"
            >
              <div class="attachment-icon">
                <file-text-icon v-if="isDocumentFile(attachment.name)" class="icon" />
                <image-icon v-else-if="isImageFile(attachment.name)" class="icon" />
                <file-icon v-else class="icon" />
              </div>
              <div class="attachment-info">
                <div class="attachment-name">{{ attachment.name }}</div>
                <div class="attachment-size">{{ formatFileSize(attachment.size) }}</div>
              </div>
              <div class="attachment-actions">
                <eye-icon
                  v-if="canPreviewFile(attachment.name)"
                  class="icon-small"
                  title="预览"
                  @click.stop="previewAttachment(attachment)"
                />
                <download-icon class="icon-small" title="下载" @click.stop="downloadAttachment(attachment)" />
              </div>
            </div>
          </div>
        </div>

        <!-- 邮件内容区域 -->
        <div class="email-content-wrapper">
          <!-- 原始邮件内容 -->
          <div v-show="!showTranslation" class="original-content">
            <div class="email-body" v-html="selectedEmail.body"></div>
            <div class="email-signature" v-if="selectedEmail.signature">
              <div class="signature-content" v-html="selectedEmail.signature"></div>
            </div>
          </div>



          <!-- 翻译结果 -->
          <div class="translation-result" v-if="showTranslation">
            <div class="translation-header">
              <div>翻译结果 ({{ getLanguageName(translationLanguage) }})</div>
              <div class="translation-actions">
                <button class="view-original" @click="toggleOriginalView">
                  {{ showOriginalContent ? '隐藏原文' : '显示原文' }}
                </button>
                <button class="close-translation" @click="closeTranslation">
                  <x-icon class="icon-tiny" /> 关闭
                </button>
              </div>
            </div>
            <div class="translation-content">
              <div v-if="isTranslating" class="translation-loading">
                <rotate-cw-icon class="icon-small spin" /> 正在翻译...
              </div>
              <div v-else class="translated-text" v-html="translatedContent"></div>
            </div>

            <!-- 原文内容（可切换显示） -->
            <div v-if="showOriginalContent" class="original-content-preview">
              <div class="original-content-header">原文内容</div>
              <div class="email-body" v-html="selectedEmail.body"></div>
              <div class="email-signature" v-if="selectedEmail.signature">
                <div class="signature-header">签名</div>
                <div class="signature-content" v-html="selectedEmail.signature"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="no-data-container">
          <img
            class="no-data"
            src="@/assets/img/noMail.png">
          <div class="no-data-name">没有查到邮件</div>
      </div>
      </div>

    </div>

    <!-- 邮件编辑器视图 -->
  <div v-else-if="currentView === 'compose'">
      <EmailComposer
        @close="currentView = 'inbox'"
        :initial-data="composeData"
        :compose-mode="composeMode"
        :replying-to="replyingTo"
      ></EmailComposer>
  </div>

    <!-- 标签管理模态框 -->
    <TagManagement
      ref="tagManagement"
      :visible="showTagModal"
      :tags="tags"
      :custom-tags-list="customTags"
      :selected-tags="currentSelectedTags"
      @close="closeTagModal"
      @save="handleTagSave"
      @edit-tag="editTagFromModal"
      @tag-selected="handleTagSelected"
    />

    <!-- 归档文件夹管理模态框 -->
    <div class="modal-overlay" v-if="showFolderModal" @click="closeFolderModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ editingFolder ? '编辑归档文件夹' : '创建新归档文件夹' }}</h3>
          <button class="close-modal" @click="closeFolderModal">
            <x-icon class="icon-small" />
          </button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label>文件夹名称</label>
            <input type="text" v-model="folderForm.name" placeholder="请输入文件夹名称" />
          </div>
          <div class="form-group">
            <label>文件夹颜色</label>
            <div class="color-picker">
              <div
                v-for="(color, index) in folderColors"
                :key="index"
                class="color-option"
                :style="{ backgroundColor: color }"
                :class="{ 'active': folderForm.color === color }"
                @click="folderForm.color = color"
              ></div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="cancel-btn" @click="closeFolderModal">取消</button>
          <button class="save-btn" @click="saveFolder">保存</button>
        </div>
      </div>
    </div>

    <!-- 归档选项模态框 -->
    <div class="modal-overlay" v-if="showArchiveModal" @click="closeArchiveModal">
      <div class="modal-content archive-modal" @click.stop>
        <div class="modal-header">
          <h3>归档邮件</h3>
          <button class="close-modal" @click="closeArchiveModal">
            <x-icon class="icon-small" />
          </button>
        </div>
        <div class="modal-body">
          <p>请选择要归档到的文件夹：</p>
          <div class="archive-folders-list">
            <div
              v-for="(folder, index) in archiveFolders"
              :key="index"
              class="archive-folder-option"
              @click="archiveEmail(folder.id)"
            >
              <folder-icon class="icon-small" :style="{ color: folder.color }" />
              {{ folder.name }}
              <span class="folder-count">{{ getArchiveFolderCount(folder.id) }}</span>
            </div>
            <div
              class="archive-folder-option create-new"
              @click="openFolderModalFromArchive"
            >
              <plus-icon class="icon-small" />
              创建新文件夹
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="cancel-btn" @click="closeArchiveModal">取消</button>
        </div>
      </div>
    </div>

    <!-- 附件预览模态框 -->
    <div class="modal-overlay" v-if="showPreviewModal" @click="closePreviewModal">
      <div class="preview-modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ previewingAttachment ? previewingAttachment.name : '附件预览' }}</h3>
          <button class="close-modal" @click="closePreviewModal">
            <x-icon class="icon-small" />
          </button>
        </div>
        <div class="preview-modal-body">
          <!-- 图片预览 -->
          <div v-if="previewingAttachment && isImageFile(previewingAttachment.name)" class="image-preview">
            <img :src="getPreviewUrl(previewingAttachment)" alt="图片预览" />
          </div>

          <!-- PDF预览 -->
          <div v-else-if="previewingAttachment && isPdfFile(previewingAttachment.name)" class="pdf-preview">
            <iframe :src="getPreviewUrl(previewingAttachment)" width="100%" height="500"></iframe>
          </div>

          <!-- 文本预览 -->
          <div v-else-if="previewingAttachment && isTextFile(previewingAttachment.name)" class="text-preview">
            <pre>{{ previewContent }}</pre>
          </div>

          <!-- 其他文件类型 -->
          <div v-else class="no-preview">
            <file-icon class="large-icon" />
            <p>无法预览此类型的文件</p>
            <p>文件类型: {{ previewingAttachment ? getFileExtension(previewingAttachment.name) : '' }}</p>
          </div>
        </div>
        <div class="preview-modal-footer">
          <button class="download-btn" @click="downloadAttachment(previewingAttachment)">
            <download-icon class="icon-small" /> 下载
          </button>
        </div>
      </div>
    </div>

    <!-- 全屏查看邮件 -->
    <div class="fullscreen-container" v-if="showFullscreenView">
      <fullscreen-email-view
        :email="selectedEmail"
        :tags="tags"
        @close="closeFullscreenView"
        @download-attachment="downloadAttachment"
        @print="printEmail"
      />
    </div>

    <!-- 确认对话框 -->
    <confirm-dialog
      :visible="showDeleteConfirmDialog"
      :title="deleteConfirmType === 'permanent' ? '确认彻底删除' : deleteConfirmType === 'trash' ? '确认移入垃圾箱' : '确认删除'"
      :message="deleteConfirmMessage"
      :is-danger="deleteConfirmType === 'permanent'"
      @confirm="confirmDeleteEmail"
      @cancel="cancelDeleteEmail"
    />

    <!-- 提醒设置对话框 -->
    <reminder-dialog
      :visible="showReminderDialog"
      :email-subject="selectedEmail ? selectedEmail.subject : ''"
      @confirm="saveEmailReminder"
      @cancel="showReminderDialog = false"
    />

    <!-- 文件上传错误提示 -->
    <div class="toast-container" v-if="showToast">
      <div class="toast" :class="toastType">
        <alert-circle-icon v-if="toastType === 'error'" class="icon-small" />
        <check-circle-icon v-else class="icon-small" />
        {{ toastMessage }}
      </div>
    </div>

    <!-- 高级搜索弹框 -->
    <advanced-search-modal
      :visible="showAdvancedSearchModal"
      :tags="tags"
      @close="closeAdvancedSearchModal"
      @search="handleAdvancedSearch"
    />


  </div>
</template>

<script>
import {
  ChevronDown, ChevronRight, ChevronLeft, Mail, Star, Users, Clock, List,
  Folder, Inbox, Send, Archive, File, Reply, Trash, Filter, Check, Paperclip,
  MessageSquare, CornerDownLeft, CornerUpLeft, ExternalLink, Grid, Layout,
  Monitor, Book, Globe, languages, RotateCw, Square, MoreVertical, Maximize,
  X, Eye, CheckSquare, Circle, FileText, Image, Hash, FolderPlus, ShoppingBag,
  Code, CreditCard, Target, Zap, Type, Bold, Italic, Underline, Strikethrough,
  RotateCcw, AlignLeft, AlignCenter, AlignRight, AlignJustify, ListOrdered,
  Indent, Table, Link, Minus, Smile, Frown, Film, Scissors, Link2, Columns,
  Maximize2, Save, Search, XCircle, Sliders, Tag, Plus, Edit, Trash2,
  ReplyAll, Forward, Download, UploadCloud, AlertCircle, CheckCircle,
  Calendar, CalendarMinus,Languages
} from 'lucide-vue'
import EmailComposer from './components/EmailComposer'
import TagManagement from './components/TagManagement'
import CustomerEmail from './components/CustomerEmail'
import EmailToolbar from './components/EmailToolbar.vue'
import ConfirmDialog from './components/ConfirmDialog.vue'
import ReminderDialog from './components/ReminderDialog.vue'
import FullscreenEmailView from './components/FullscreenEmailView.vue'
import EmailTabs from './components/EmailTabs.vue'
import AdvancedSearchModal from './components/AdvancedSearchModal.vue'

export default {
  name: 'EmailClient',
  components: {
    ChevronDownIcon: ChevronDown,
    ChevronRightIcon: ChevronRight,
    ChevronLeftIcon: ChevronLeft,
    MailIcon: Mail,
    StarIcon: Star,
    UsersIcon: Users,
    ClockIcon: Clock,
    ListIcon: List,
    FolderIcon: Folder,
    InboxIcon: Inbox,
    SendIcon: Send,
    ArchiveIcon: Archive,
    FileIcon: File,
    ReplyIcon: Reply,
    TrashIcon: Trash,
    FilterIcon: Filter,
    CheckIcon: Check,
    PaperclipIcon: Paperclip,
    MessageSquareIcon: MessageSquare,
    CornerDownLeftIcon: CornerDownLeft,
    CornerUpLeftIcon: CornerUpLeft,
    ExternalLinkIcon: ExternalLink,
    GridIcon: Grid,
    LayoutIcon: Layout,
    MonitorIcon: Monitor,
    BookIcon: Book,
    GlobeIcon: Globe,
    LanguagesIcon: Languages,
    RotateCwIcon: RotateCw,
    SquareIcon: Square,
    MoreVerticalIcon: MoreVertical,
    MaximizeIcon: Maximize,
    XIcon: X,
    EyeIcon: Eye,
    CheckSquareIcon: CheckSquare,
    CircleIcon: Circle,
    FileTextIcon: FileText,
    ImageIcon: Image,
    HashIcon: Hash,
    FolderPlusIcon: FolderPlus,
    ShoppingBagIcon: ShoppingBag,
    CodeIcon: Code,
    CreditCardIcon: CreditCard,
    TargetIcon: Target,
    ZapIcon: Zap,
    TypeIcon: Type,
    BoldIcon: Bold,
    ItalicIcon: Italic,
    UnderlineIcon: Underline,
    StrikethroughIcon: Strikethrough,
    RotateCcwIcon: RotateCcw,
    AlignLeftIcon: AlignLeft,
    AlignCenterIcon: AlignCenter,
    AlignRightIcon: AlignRight,
    AlignJustifyIcon: AlignJustify,
    ListOrderedIcon: ListOrdered,
    IndentIcon: Indent,
    TableIcon: Table,
    LinkIcon: Link,
    MinusIcon: Minus,
    SmileIcon: Smile,
    FrownIcon: Frown,
    FilmIcon: Film,
    ScissorsIcon: Scissors,
    Link2Icon: Link2,
    ColumnsIcon: Columns,
    Maximize2Icon: Maximize2,
    SaveIcon: Save,
    SearchIcon: Search,
    XCircleIcon: XCircle,
    SlidersIcon: Sliders,
    TagIcon: Tag,
    PlusIcon: Plus,
    EditIcon: Edit,
    Trash2Icon: Trash2,
    ReplyAllIcon: ReplyAll,
    ForwardIcon: Forward,
    DownloadIcon: Download,
    UploadCloudIcon: UploadCloud,
    AlertCircleIcon: AlertCircle,
    CheckCircleIcon: CheckCircle,
    CalendarIcon: Calendar,
    CalendarMinusIcon: CalendarMinus,
    EmailComposer,
    TagManagement,
    CustomerEmail,
    EmailToolbar,
    ConfirmDialog,
    ReminderDialog,
    FullscreenEmailView,
    EmailTabs,
    AdvancedSearchModal
  },
  data() {
    return {
      currentView: 'inbox', // 'inbox' 或 'compose'
      activeTab: 'email', // 'email' 或 'customer'
      selectedContact: null, // 存储当前选中的客户联系人
      selectedCustomer: null, // 存储当前选中的客户

      // 新增属性 - 邮件工具栏相关
      showDeleteConfirmDialog: false,
      deleteConfirmType: 'normal', // 'normal', 'permanent', 'trash'
      deleteConfirmMessage: '',

      // 修改主题相关
      showEditSubjectDialog: false,
      editedSubject: '',

      // 提醒设置相关
      showReminderDialog: false,
      emailReminders: [], // 存储邮件提醒

      // 全屏查看相关
      emailTabs: [], // 存储全屏查看的邮件标签页
      activeTabIndex: 0,
      sections: {
        common: true,   // 默认展开"常用功能"分类
        account: true,  // 默认展开"我的全部账号"分类
        query: false,   // 默认折叠"查询箱"分类
        tags: false,    // 默认折叠"标签邮件"分类
        archives: false // 默认折叠"归档文件夹"分类
      },
      // 标签分组展开状态
      tagGroups: {
        system: true,
        custom: true
      },
      selectedEmail: null,
      // 客户联系人相关的邮件数据
      contactEmails: [
        {
          id: 101,
          sender: '<EMAIL>',
          tag: '3P-PRECISION',
          time: '2024/11/25 14:30',
          subject: 'Re: Inquiry about precision parts manufacturing',
          hasAttachment: true,
          hasReply: false,
          recipients: ['钟秋玲', '销售部'],
          ccRecipients: ['技术部'],
          fullDate: '2024/11/25 14:30',
          body: `<p>Dear Benda Team,</p><p>Thank you for your prompt response to our inquiry. We have reviewed your proposal and are interested in proceeding with the sample order.</p><p>Please find attached our technical specifications for the parts we need.</p><p>Looking forward to your confirmation.</p><p>Best regards,</p>`,
          signature: `<p><strong>John Miller</strong><br>Purchasing Manager<br>3P-PRECISION Engineering</p>`,
          tags: [3, 8],
          attachments: [
            { name: 'technical_specs_v2.pdf', size: 3200000, type: 'document' },
            { name: 'sample_requirements.xlsx', size: 1500000, type: 'document' }
          ],
          read: true,
          receivedDate: new Date('2024-11-25T14:30:00'),
          sendDate: new Date('2024-11-25T14:25:00'),
          size: 4700000,
          starred: true,
          folderName: '收件箱',
          account: '<EMAIL>',
          owner: '钟秋玲',
          contactId: 1 // 关联到联系人ID
        },
        {
          id: 102,
          sender: '<EMAIL>',
          tag: 'AAKAR FOUNDRY',
          time: '2024/11/22 09:15',
          subject: 'Price quotation for die casting molds',
          hasAttachment: false,
          hasReply: true,
          recipients: ['钟秋玲'],
          ccRecipients: [],
          fullDate: '2024/11/22 09:15',
          body: `<p>Dear Benda Team,</p><p>We are interested in your die casting mold manufacturing services. Could you please provide us with a quotation for the following items:</p><ul><li>Aluminum die casting mold for automotive parts</li><li>Zinc die casting mold for electronic housing</li></ul><p>We need these molds for our new product line launching in Q2 2025.</p><p>Thank you.</p>`,
          signature: `<p><strong>Rajesh Kumar</strong><br>Procurement Director<br>AAKAR FOUNDRY</p>`,
          tags: [9, 4],
          read: false,
          receivedDate: new Date('2024-11-22T09:15:00'),
          sendDate: new Date('2024-11-22T09:10:00'),
          size: 1200000,
          starred: false,
          folderName: '收件箱',
          account: '<EMAIL>',
          owner: '钟秋玲',
          contactId: 2 // 关联到联系人ID
        },
        {
          id: 103,
          sender: '<EMAIL>',
          tag: 'ABF LT,JSC',
          time: '2024/11/18 16:45',
          subject: 'Order confirmation - Project ABF-2024-11',
          hasAttachment: true,
          hasReply: false,
          recipients: ['钟秋玲', '销售部', '抄送: 财务部'],
          ccRecipients: ['财务部'],
          fullDate: '2024/11/18 16:45',
          body: `<p>Dear Benda Team,</p><p>We are pleased to confirm our order for the following items:</p><ul><li>5 sets of die casting molds as per our previous discussion</li><li>Technical support for initial setup</li></ul><p>Please find attached our official purchase order and payment details.</p><p>We look forward to a successful collaboration.</p>`,
          signature: `<p><strong>Viktor Petrov</strong><br>Chief Operations Officer<br>ABF LT, JSC</p>`,
          tags: [7, 6],
          attachments: [
            { name: 'purchase_order_ABF-2024-11.pdf', size: 2800000, type: 'document' },
            { name: 'payment_details.pdf', size: 1200000, type: 'document' }
          ],
          read: true,
          receivedDate: new Date('2024-11-18T16:45:00'),
          sendDate: new Date('2024-11-18T16:40:00'),
          size: 4000000,
          starred: true,
          archiveFolder: 3,
          folderName: '已归档',
          account: '<EMAIL>',
          owner: '钟秋玲',
          contactId: 3 // 关联到联系人ID
        }
      ],

      // 常规邮件数据
      emails: [
        {
          id: 1,
          sender: 'Oguzhan Tan',
          tag: 'Arıcıoğlu',
          time: '2024/11/30 07:45',
          subject: 'RE: Re: : RE: RE: new offer as 2 models 1 set for 18"...',
          hasAttachment: true,
          hasReply: false,
          recipients: ['钟秋玲', '财神爷'],
          ccRecipients: ['黄文秀', '钟秋玲', '罗文欣'],
          fullDate: '2024/11/30 07:45',
          body: `<p>Hi dear,</p><p>We are working on your offer.</p><p>I hope i will back to you ASAP.</p><p>Thanks.</p>`,
          signature: `<p>Saygılarımla/Best Regards</p><p><strong>Oğuzhan TAN</strong></p><p>Tasarım ve Ürün Geliştirme Şefi / Design and Product Development Chief</p><hr/><div style="display: flex;"><div style="margin-right: 20px;"><img src="@/assets/img/examine_head.png?height=60&width=60" alt="Logo" /></div><div><p><strong>Arıcıoğlu Plaza</strong></p><p>Havaalanı Mah. Mehmet Akif İnan Cad. N:67<br/>Altşehir – Esenler / İstanbul</p><p>T: +90 (212) 569 45 45<br/>M: +90 (552) 042 19 60<br/><a href="http://www.aricioglu.com">www.aricioglu.com</a></p></div></div>`,
          tags: [1, 3],
          attachments: [
            { name: 'product_catalog.pdf', size: 2500000, type: 'document' },
            { name: 'price_list_2024.xlsx', size: 1100000, type: 'document' }
          ],
          read: false,
          receivedDate: new Date('2024-11-30T07:45:00'),
          sendDate: new Date('2024-11-30T07:40:00'),
          size: 3600000, // 3.6 MB
          starred: true,
          archiveFolder: 1,
          folderName: '待处理',
          account: '<EMAIL>',
          owner: '钟秋玲'
        },
        {
          id: 2,
          sender: 'Dennis Weiss',
          tag: 'NORCK GmbH',
          time: '2024/11/26 19:40',
          subject: 'Re: Re: NORCK GmbH Supplier Registration Re...',
          hasAttachment: true,
          hasReply: false,
          recipients: ['钟秋玲', '采购部'],
          ccRecipients: [],
          fullDate: '2024/11/26 19:40',
          body: `<p>Dear Supplier,</p><p>Thank you for your interest in registering with NORCK GmbH. We have received your application and are currently reviewing it.</p><p>Please find attached our supplier guidelines and quality requirements document.</p><p>Best regards,</p>`,
          signature: `<p><strong>Dennis Weiss</strong><br>Supplier Relations Manager<br>NORCK GmbH</p>`,
          tags: [2],
          attachments: [
            { name: 'supplier_guidelines.pdf', size: 3700000, type: 'document' },
            { name: 'quality_requirements.pdf', size: 1800000, type: 'document' }
          ],
          read: true,
          receivedDate: new Date('2024-11-26T19:40:00'),
          sendDate: new Date('2024-11-26T19:35:00'),
          size: 5500000, // 5.5 MB
          starred: false,
          archiveFolder: 2,
          folderName: '供应商',
          account: '<EMAIL>',
          owner: '钟秋玲'
        },
        {
          id: 3,
          sender: 'Marco Bianchi',
          tag: 'pms-system',
          time: '2024/11/21 17:37',
          subject: 'Letto: Re: Re: Project F.T. 1610---Enclosed is the upd...',
          hasAttachment: false,
          hasReply: true,
          recipients: ['钟秋玲'],
          ccRecipients: [],
          fullDate: '2024/11/21 17:37',
          body: `<p>This is a read receipt for your email regarding Project F.T. 1610.</p><p>The message was read on Nov 21, 2024 at 17:37.</p>`,
          tags: [1],
          read: false,
          receivedDate: new Date('2024-11-21T17:37:00'),
          sendDate: new Date('2024-11-21T17:37:00'),
          size: 850000, // 850 KB
          starred: false,
          folderName: '收件箱',
          account: '<EMAIL>',
          owner: '钟秋玲'
        },
        {
          id: 4,
          sender: 'Marco Bianchi',
          tag: 'pms-system',
          time: '2024/11/21 17:36',
          subject: 'Read: RE: Re: Re: Project F.T. 1610---Enclosed is the...',
          hasAttachment: false,
          hasReply: true,
          recipients: ['钟秋玲'],
          fullDate: '2024/11/21 17:36',
          body: `<p>Your message has been read.</p>`,
          tags: [],
          read: true,
          receivedDate: new Date('2024-11-21T17:36:00'),
          starred: false
        },
        {
          id: 5,
          sender: 'Kawasaki India',
          tag: 'india Kawasaki',
          time: '2024/11/21 15:45',
          subject: 'Re: Re: High pressure Die casting mold manufacturer ...',
          hasAttachment: true,
          hasReply: false,
          recipients: ['钟秋玲', '技术部', '抄送: 生产部'],
          fullDate: '2024/11/21 15:45',
          body: `<p>Dear Team,</p><p>Thank you for your quotation for the high pressure die casting molds. We have reviewed your proposal and have some technical questions.</p><p>Please see the attached drawings with our comments and questions.</p><p>We look forward to your response.</p><p>Regards,</p>`,
          signature: `<p><strong>Kawasaki India Engineering Team</strong><br>Kawasaki Heavy Industries Ltd.<br>India Operations</p>`,
          tags: [2, 3],
          attachments: [
            { name: 'technical_drawing_rev2.dwg', size: 5200000, type: 'document' },
            { name: 'mold_specifications.pdf', size: 2300000, type: 'document' },
            { name: 'part_photo.jpg', size: 1100000, type: 'image' }
          ],
          read: false,
          receivedDate: new Date('2024-11-21T15:45:00'),
          starred: true,
          archiveFolder: 3
        },
        {
          id: 6,
          sender: 'John Smith',
          tag: 'ABC Corp',
          time: '2024/05/18 09:30',
          subject: 'Today\'s meeting agenda',
          hasAttachment: false,
          hasReply: false,
          recipients: ['钟秋玲'],
          fullDate: '2024/05/18 09:30',
          body: `<p>Hello,</p><p>Here's the agenda for today's meeting.</p><p>Best regards,</p>`,
          tags: [2],
          read: false,
          receivedDate: new Date('2024-05-18T09:30:00'), // Today
          starred: false
        },
        {
          id: 7,
          sender: 'Jane Doe',
          tag: 'XYZ Inc',
          time: '2024/05/17 14:20',
          subject: 'Yesterday\'s project update',
          hasAttachment: false,
          hasReply: false,
          recipients: ['钟秋玲'],
          fullDate: '2024/05/17 14:20',
          body: `<p>Hi,</p><p>Here's the update from yesterday's project meeting.</p><p>Regards,</p>`,
          tags: [2],
          read: true,
          receivedDate: new Date('2024-05-17T14:20:00'), // Yesterday
          starred: false
        }
      ],
      composeData: {
        from: '<EMAIL>',
        to: '',
        cc: '',
        bcc: '',
        subject: '',
        content: '',
        attachments: []
      },
      composeMode: 'new', // 'new', 'reply', 'replyAll', 'forward'
      replyingTo: null, // 存储正在回复的邮件
      searchData: {
        term: '',
        filter: 'all', // 'all', 'subject', 'sender', 'content'
        isSearchActive: false,
        showAdvanced: false
      },
      // 标签相关数据
      tags: [
        // 系统标签 (ID 1-9)
        { id: 1, name: '通知', color: '#e60012' },
        { id: 2, name: '招聘', color: '#e60012' },
        { id: 3, name: '商机', color: '#e60012' },
        { id: 4, name: '报价', color: '#e60012' },
        { id: 5, name: '已更回复', color: '#e60012' },
        { id: 6, name: 'PI', color: '#e60012' },
        { id: 7, name: '订单', color: '#e60012' },
        { id: 8, name: '样品', color: '#e60012' },
        { id: 9, name: '询盘', color: '#e60012' },
        // 自定义标签 (ID > 9)
        { id: 10, name: '客户投诉', color: '#4caf50' },
        { id: 11, name: '业务跟进', color: '#9c27b0' },
        { id: 12, name: '客户不感兴趣', color: '#795548' },
        { id: 13, name: '重点客户', color: '#00bcd4' },
        { id: 14, name: '三次未回复', color: '#ff9800' }
      ],
      tagColors: [
        '#e60012', '#ff9800', '#ffeb3b', '#4caf50', '#2196f3',
        '#9c27b0', '#795548', '#607d8b', '#f44336', '#3f51b5'
      ],
      showTagModal: false,
      tagForm: {
        id: null,
        name: '',
        color: '#e60012'
      },
      editingTag: false,
      showTagSelector: false,
      activeTag: null,
      currentSelectedTags: [], // 存储当前选中邮件的标签状态
      activeFolder: 'inbox',
      activeFilter: null, // 'unread', 'today', 'yesterday', 'starred'

      // 归档文件夹相关数据
      archiveFolders: [
        { id: 1, name: '项目文件', color: '#4caf50' },
        { id: 2, name: '供应商', color: '#2196f3' },
        { id: 3, name: '客户询盘', color: '#ff9800' }
      ],
      folderColors: [
        '#4caf50', '#2196f3', '#ff9800', '#e60012', '#9c27b0',
        '#795548', '#607d8b', '#f44336', '#3f51b5', '#009688'
      ],
      showFolderModal: false,
      folderForm: {
        id: null,
        name: '',
        color: '#4caf50'
      },
      editingFolder: false,
      activeArchiveFolder: null,
      showArchiveModal: false,
      emailToArchive: null,

      // 附件相关数据
      showAttachmentOptions: false,
      maxFileSize: 25 * 1024 * 1024, // 25MB
      maxTotalSize: 50 * 1024 * 1024, // 50MB
      allowedFileTypes: [
        // 文档
        '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt',
        // 图片
        '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp',
        // 压缩文件
        '.zip', '.rar', '.7z',
        // 其他常见格式
        '.csv', '.json', '.xml', '.html', '.dwg', '.dxf'
      ],

      // 附件预览相关
      showPreviewModal: false,
      previewingAttachment: null,
      previewContent: '',
      previewUrl: '',

      // 拖放相关
      isDraggingOver: false,
      dragCounter: 0,

      // 提示消息
      showToast: false,
      toastMessage: '',
      toastType: 'success', // 'success' 或 'error'
      toastTimeout: null,

      // 全屏查看
      showFullscreenView: false,

      // 抄送人展开/收起状态
      showAllCc: false,

      // 翻译相关
      showTranslation: false,
      translationLanguage: 'zh', // 默认翻译为中文
      translationSourceLanguage: 'auto', // 默认自动检测源语言
      translatedContent: '',
      isTranslating: false,
      showLanguageSelector: false,
      showOriginalContent: false, // 是否在翻译结果中显示原文
      languages: [
        { code: 'zh', name: '中文' },
        { code: 'en', name: '英文' },
        { code: 'ja', name: '日语' },
        { code: 'ko', name: '韩语' },
        { code: 'fr', name: '法语' },
        { code: 'de', name: '德语' },
        { code: 'es', name: '西班牙语' },
        { code: 'ru', name: '俄语' }
      ],

      // 高级搜索相关
      showAdvancedSearchModal: false,
      advancedSearchFilters: {},

      // AI智能摘要相关
      showAiSummary: false,
      isGeneratingSummary: false,
      aiSummaryContent: ''
    }
  },
  mounted() {
    // 页面加载时默认选择"我的全部账号"下的"待处理"选项
    this.$nextTick(() => {
      // 设置筛选条件为"待处理"
      this.filterByPending();
    });
  },
  computed: {
    // 获取自定义标签列表
    customTags() {
      // 自定义标签ID大于9
      return this.tags.filter(tag => tag.id > 9);
    },

    // 获取所有可用的邮件（包括常规邮件和联系人邮件）
    allEmails() {
      return [...this.emails, ...this.contactEmails];
    },

    // 根据搜索条件、标签和特殊筛选条件筛选邮件
    filteredEmails() {
      // 根据当前选中的联系人或客户筛选邮件
      let result;

      if (this.selectedContact) {
        // 如果选中了联系人，显示与该联系人相关的邮件
        result = this.contactEmails.filter(email => email.contactId === this.selectedContact.id);
      } else if (this.selectedCustomer) {
        // 如果选中了客户，显示与该客户相关的邮件
        result = this.contactEmails.filter(email => email.customerId === this.selectedCustomer.id);
      } else {
        // 否则显示所有常规邮件
        result = [...this.emails];
      }

      // 如果有搜索条件，先按搜索条件筛选
      if (this.searchData.isSearchActive && this.searchData.term) {
        const term = this.searchData.term.toLowerCase();
        const filter = this.searchData.filter;

        result = result.filter(email => {
          if (filter === 'all') {
            return email.subject.toLowerCase().includes(term) ||
                   email.sender.toLowerCase().includes(term) ||
                   (email.body && email.body.toLowerCase().includes(term));
          } else if (filter === 'subject') {
            return email.subject.toLowerCase().includes(term);
          } else if (filter === 'sender') {
            return email.sender.toLowerCase().includes(term);
          } else if (filter === 'content') {
            return email.body && email.body.toLowerCase().includes(term);
          }
          return false;
        });
      }

      // 如果有标签筛选，再按标签筛选
      if (this.activeTag) {
        result = result.filter(email =>
          email.tags && email.tags.includes(this.activeTag)
        );
      }

      // 如果有特殊筛选条件，按特殊条件筛选
      if (this.activeFilter) {
        if (this.activeFilter === 'pending') {
          // 筛选待处理的邮件（未读或者标记为重要的邮件）
          result = result.filter(email => !email.read || email.starred);
        } else if (this.activeFilter === 'unread') {
          // 筛选未读邮件
          result = result.filter(email => !email.read);
        } else if (this.activeFilter === 'today') {
          // 筛选当日收到的邮件
          const today = new Date();
          today.setHours(0, 0, 0, 0);

          result = result.filter(email => {
            const emailDate = new Date(email.receivedDate);
            emailDate.setHours(0, 0, 0, 0);
            return emailDate.getTime() === today.getTime();
          });
        } else if (this.activeFilter === 'yesterday') {
          // 筛选昨日收到的邮件
          const yesterday = new Date();
          yesterday.setDate(yesterday.getDate() - 1);
          yesterday.setHours(0, 0, 0, 0);

          result = result.filter(email => {
            const emailDate = new Date(email.receivedDate);
            emailDate.setHours(0, 0, 0, 0);
            return emailDate.getTime() === yesterday.getTime();
          });
        } else if (this.activeFilter === 'starred') {
          // 筛选星标邮件
          result = result.filter(email => email.starred);
        }
      }

      // 如果有归档文件夹筛选，按归档文件夹筛选
      if (this.activeArchiveFolder) {
        result = result.filter(email => email.archiveFolder === this.activeArchiveFolder);
      }

      // 应用高级搜索筛选
      if (Object.keys(this.advancedSearchFilters).length > 0) {
        result = result.filter(email => {
          // 检查每个筛选条件
          for (const [key, value] of Object.entries(this.advancedSearchFilters)) {
            switch (key) {
              case 'tag':
                if (!email.tags || !email.tags.includes(value)) return false;
                break;
              case 'folder':
                if (email.folderName !== value &&
                    !(value === 'archive' && email.archiveFolder)) return false;
                break;
              case 'customer':
                if (!email.tag || !email.tag.toLowerCase().includes(value.toLowerCase())) return false;
                break;
              case 'sender':
                if (!email.sender || !email.sender.toLowerCase().includes(value.toLowerCase())) return false;
                break;
              case 'recipient':
                const recipients = [...(email.recipients || []), ...(email.ccRecipients || [])];
                if (!recipients.some(r => r.toLowerCase().includes(value.toLowerCase()))) return false;
                break;
              case 'to':
                if (!email.recipients || !email.recipients.some(r => r.toLowerCase().includes(value.toLowerCase()))) return false;
                break;
              case 'subject':
                if (!email.subject || !email.subject.toLowerCase().includes(value.toLowerCase())) return false;
                break;
              case 'content':
                if (!email.body || !this.stripHtml(email.body).toLowerCase().includes(value.toLowerCase())) return false;
                break;
              case 'startTime':
                if (!email.receivedDate || new Date(email.receivedDate) < new Date(value)) return false;
                break;
              case 'endTime':
                if (!email.receivedDate || new Date(email.receivedDate) > new Date(value)) return false;
                break;
              case 'hasAttachment':
                const hasAttachments = !!(email.attachments && email.attachments.length > 0);
                if (hasAttachments !== value) return false;
                break;
              case 'attachmentName':
                if (!email.attachments || !email.attachments.some(a => a.name.toLowerCase().includes(value.toLowerCase()))) return false;
                break;
              case 'readStatus':
                if (email.read !== value) return false;
                break;
              case 'starred':
                if (email.starred !== value) return false;
                break;
              case 'replyStatus':
                const hasReply = !!email.hasReply;
                if ((value === 'replied' && !hasReply) || (value === 'not_replied' && hasReply)) return false;
                break;
            }
          }
          return true;
        });
      }

      // 对结果进行排序，星标邮件置顶
      result.sort((a, b) => {
        // 首先按星标状态排序（星标邮件置顶）
        if (a.starred && !b.starred) return -1;
        if (!a.starred && b.starred) return 1;

        // 如果星标状态相同，则按接收时间倒序排序（新邮件在前）
        if (a.receivedDate && b.receivedDate) {
          return new Date(b.receivedDate) - new Date(a.receivedDate);
        }

        return 0;
      });

      return result;
    },

    // 当前筛选名称
    currentFilterName() {
      if (this.activeTag) {
        return `标签: ${this.getTagName(this.activeTag)}`;
      } else if (this.activeFilter) {
        return this.getFilterName(this.activeFilter);
      } else if (this.activeArchiveFolder) {
        return `归档文件夹: ${this.getArchiveFolderName(this.activeArchiveFolder)}`;
      } else if (this.activeFolder === 'inbox') {
        return '收件箱';
      } else if (this.activeFolder === 'sent') {
        return '发件箱';
      } else if (this.activeFolder === 'archive') {
        return '已发件箱';
      } else if (this.activeFolder === 'draft') {
        return '草稿箱';
      } else if (this.activeFolder === 'recycle') {
        return '回收站';
      } else if (this.activeFolder === 'spam') {
        return '垃圾邮件箱';
      } else if (Object.keys(this.advancedSearchFilters).length > 0) {
        // 将高级搜索移到最后，优先显示其他分类
        return '高级搜索';
      }
      return '收件箱';
    },

    // 判断是否有上一封邮件
    hasPreviousEmail() {
      if (!this.selectedEmail || this.filteredEmails.length <= 1) return false;

      const currentIndex = this.filteredEmails.findIndex(email => email.id === this.selectedEmail.id);
      return currentIndex > 0;
    },

    // 判断是否有下一封邮件
    hasNextEmail() {
      if (!this.selectedEmail || this.filteredEmails.length <= 1) return false;

      const currentIndex = this.filteredEmails.findIndex(email => email.id === this.selectedEmail.id);
      return currentIndex < this.filteredEmails.length - 1 && currentIndex !== -1;
    }
  },
  methods: {
    // 自动选择第一封邮件
    selectFirstEmail() {
      // 确保有邮件列表且没有已选中的邮件
      if (this.filteredEmails && this.filteredEmails.length > 0 && !this.selectedEmail) {
        // 选择第一封邮件
        this.selectEmail(this.filteredEmails[0]);
      }
    },

    selectEmail(email) {
      this.selectedEmail = email;
      this.showTagSelector = false;

      // 标记邮件为已读
      if (!email.read) {
        email.read = true;
      }
    },
    saveEmailReminder(reminderData){
      if (!reminderData || !this.selectedEmail) return;

      // 创建新的提醒
      const reminder = {
        id: Date.now(),
        emailId: this.selectedEmail.id,
        time: reminderData.time,
        note: reminderData.note,
        completed: false
      };

      // 添加到提醒列表
      this.emailReminders.push(reminder);

      // 显示成功提示
      const reminderTime = new Date(reminder.time);
      const formattedTime = `${reminderTime.toLocaleDateString()} ${reminderTime.toLocaleTimeString()}`;
      this.showToastMessage(`已设置提醒：${formattedTime}`, 'success');

      // 关闭对话框
      this.showReminderDialog = false;
    },
    openCompose() {
      this.resetComposeData();
      this.composeMode = 'new';
      this.currentView = 'compose';
    },

    // 重置邮件编辑器数据
    resetComposeData() {
      this.composeData = {
        from: '<EMAIL>',
        to: '',
        cc: '',
        bcc: '',
        subject: '',
        content: '',
        attachments: []
      };
      this.composeMode = 'new';
      this.replyingTo = null;
    },

    // 搜索邮件
    searchEmails() {
      if (!this.searchData.term.trim()) {
        this.searchData.isSearchActive = false;
        return;
      }

      // 清除当前选中的邮件
      this.selectedEmail = null;

      this.searchData.isSearchActive = true;

      // 自动选择第一封邮件
      this.$nextTick(() => {
        this.selectFirstEmail();
      });
    },

    // 清除搜索
    clearSearch() {
      this.searchData.term = '';
      this.searchData.isSearchActive = false;

      // 清除当前选中的邮件
      this.selectedEmail = null;

      // 自动选择第一封邮件
      this.$nextTick(() => {
        this.selectFirstEmail();
      });
    },

    // 切换高级搜索选项
    toggleAdvancedSearch() {
      this.searchData.showAdvanced = !this.searchData.showAdvanced;
    },

    // 设置搜索过滤器
    setSearchFilter(filter) {
      this.searchData.filter = filter;
      if (this.searchData.term) {
        this.searchEmails();
      }
    },

    // 特殊筛选相关方法
    // 筛选待处理邮件
    filterByPending() {
      // 清除其他筛选条件
      this.activeTag = null;
      this.activeFolder = '';
      this.activeArchiveFolder = null;
      this.searchData.isSearchActive = false;
      this.searchData.term = '';

      // 清除当前选中的邮件
      this.selectedEmail = null;

      // 设置筛选条件为"待处理"
      this.activeFilter = 'pending';

      // 自动选择第一封邮件
      this.$nextTick(() => {
        this.selectFirstEmail();
      });
    },

    // 按特殊条件筛选邮件
    filterBySpecial(filter) {
      // 清除其他筛选条件
      this.activeTag = null;
      this.activeFolder = '';
      this.activeArchiveFolder = null;
      this.searchData.isSearchActive = false;
      this.searchData.term = '';

      // 清除当前选中的邮件
      this.selectedEmail = null;

      // 如果点击当前激活的筛选条件，则取消筛选
      if (this.activeFilter === filter) {
        this.activeFilter = null;
        this.activeFolder = 'inbox';
      } else {
        this.activeFilter = filter;
      }

      // 自动选择第一封邮件
      this.$nextTick(() => {
        this.selectFirstEmail();
      });
    },

    // 清除特殊筛选
    clearSpecialFilter() {
      this.activeFilter = null;
      this.activeFolder = 'inbox';
    },

    // 获取特殊筛选条件名称
    getFilterName(filter) {
      if (filter === 'pending') {
        return '待处理';
      } else if (filter === 'unread') {
        return '所有未读邮件';
      } else if (filter === 'today') {
        return '当日收到的邮件';
      } else if (filter === 'yesterday') {
        return '昨日收到的邮件';
      } else if (filter === 'starred') {
        return '星标邮件';
      }
      return '';
    },

    // 获取邮件列表标题
    getListTitle() {
      if (this.activeTag) {
        return `标签: ${this.getTagName(this.activeTag)}`;
      } else if (this.activeFilter) {
        return this.getFilterName(this.activeFilter);
      } else if (this.activeArchiveFolder) {
        return `归档文件夹: ${this.getArchiveFolderName(this.activeArchiveFolder)}`;
      } else if (this.activeFolder === 'inbox') {
        return '收件箱';
      } else if (this.activeFolder === 'sent') {
        return '发件箱';
      } else if (this.activeFolder === 'archive') {
        return '已发件箱';
      } else if (this.activeFolder === 'draft') {
        return '草稿箱';
      } else if (this.activeFolder === 'recycle') {
        return '回收站';
      } else if (this.activeFolder === 'spam') {
        return '垃圾邮件箱';
      } else if (Object.keys(this.advancedSearchFilters || {}).length > 0) {
        // 将高级搜索移到最后，优先显示其他分类
        return '高级搜索';
      }
      return '收件箱';
    },

    // 获取未读邮件数量
    getUnreadCount() {
      return this.emails.filter(email => !email.read).length;
    },

    // 获取当日收到的邮件数量
    getTodayCount() {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      return this.emails.filter(email => {
        const emailDate = new Date(email.receivedDate);
        emailDate.setHours(0, 0, 0, 0);
        return emailDate.getTime() === today.getTime();
      }).length;
    },

    // 获取昨日收到的邮件数量
    getYesterdayCount() {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      yesterday.setHours(0, 0, 0, 0);

      return this.emails.filter(email => {
        const emailDate = new Date(email.receivedDate);
        emailDate.setHours(0, 0, 0, 0);
        return emailDate.getTime() === yesterday.getTime();
      }).length;
    },

    // 标签相关方法
    // 切换标签分组的展开/折叠状态
    toggleTagGroup(group) {
      this.tagGroups[group] = !this.tagGroups[group];
    },

    // 打开标签管理模态框
    openTagModal() {
      // 确保当前邮件的标签信息已经准备好
      if (this.selectedEmail && !this.selectedEmail.tags) {
        this.selectedEmail.tags = [];
      }

      // 获取当前邮件的标签状态
      this.currentSelectedTags = this.getSelectedTagsForModal();

      // 显示模态框
      this.showTagModal = true;
    },

    // 关闭标签管理模态框
    closeTagModal() {
      this.showTagModal = false;
    },

    // 处理标签保存
    handleTagSave(data) {
      // 更新自定义标签列表
      if (data.customTags) {
        // 将自定义标签保存到应用中
        const customTagIds = data.customTags.map(tag => {
          // 确保自定义标签有正确的ID（大于9）
          let id = parseInt(tag.id.replace('custom-', ''));
          if (isNaN(id) || id <= 9) {
            id = this.getNextTagId();
          }

          return {
            id,
            name: tag.name,
            color: this.getColorFromClass(tag.colorClass)
          };
        });

        // 更新自定义标签列表（ID > 9的标签）
        this.tags = this.tags.filter(tag => tag.id <= 9).concat(customTagIds);
      }

      // 更新选中的邮件标签
      if (this.selectedEmail && data.selectedTags) {
        // 确保当前邮件有tags数组
        if (!this.selectedEmail.tags) {
          this.selectedEmail.tags = [];
        }

        // 将新选中的标签转换为ID
        const newTagIds = data.selectedTags.map(tag => {
          if (tag.type === 'system') {
            return parseInt(tag.id.replace('sys-', ''));
          } else {
            // 确保自定义标签有正确的ID（大于9）
            let id = parseInt(tag.id.replace('custom-', ''));
            if (isNaN(id) || id <= 9) {
              id = this.getNextTagId();
            }
            return id;
          }
        });

        // 合并现有标签和新标签，并去重
        this.selectedEmail.tags = [...new Set([...this.selectedEmail.tags, ...newTagIds])];

        // 如果当前有激活的标签筛选，检查是否需要更新
        if (this.activeTag && !this.selectedEmail.tags.includes(this.activeTag)) {
          // 如果当前邮件不包含激活的标签，可能需要从列表中移除
          // 这里不做处理，因为筛选是动态的，会自动更新
        }
      }

      // 更新当前选中的标签状态
      this.currentSelectedTags = this.getSelectedTagsForModal();

      this.showToastMessage('标签已更新', 'success');

      // 关闭标签管理模态框
      this.closeTagModal();
    },

    // 获取下一个可用的标签ID
    getNextTagId() {
      return Math.max(0, ...this.tags.map(t => t.id)) + 1;
    },

    // 从CSS类名获取颜色值
    getColorFromClass(colorClass) {
      const colorMap = {
        'green': '#4caf50',
        'purple': '#9c27b0',
        'brown': '#795548',
        'cyan': '#00bcd4',
        'orange': '#ff9800',
        'red': '#e60012',
        'blue': '#2196f3'
      };
      return colorMap[colorClass] || '#e60012';
    },

    // 编辑标签
    editTagFromModal(tag) {
      // 这里可以实现编辑标签的逻辑
      console.log('编辑标签:', tag);
    },

    // 处理标签实时选择
    handleTagSelected(selectedTags) {
      if (!this.selectedEmail) return;

      // 确保当前邮件有tags数组
      if (!this.selectedEmail.tags) {
        this.selectedEmail.tags = [];
      }

      // 将选中的标签转换为ID
      const tagIds = selectedTags.map(tag => {
        if (tag.type === 'system') {
          return parseInt(tag.id.replace('sys-', ''));
        } else {
          // 确保自定义标签有正确的ID（大于9）
          let id = parseInt(tag.id.replace('custom-', ''));
          if (isNaN(id) || id <= 9) {
            id = this.getNextTagId();
          }
          return id;
        }
      });

      // 更新邮件的标签
      this.selectedEmail.tags = tagIds;

      // 更新当前选中的标签状态
      this.currentSelectedTags = this.getSelectedTagsForModal();

      // 如果当前有激活的标签筛选，检查是否需要更新视图
      if (this.activeTag && !tagIds.includes(this.activeTag)) {
        // 邮件不再包含当前筛选的标签，但筛选是动态的，会自动更新
        // 这里不需要额外处理
      }
    },

    // 获取用于模态框的已选标签
    getSelectedTagsForModal() {
      if (!this.selectedEmail || !this.selectedEmail.tags) return [];

      // 将当前邮件的标签ID转换为TagManagement组件需要的格式
      return this.selectedEmail.tags.map(tagId => {
        // 检查是否是系统标签（1-9）
        if (tagId >= 1 && tagId <= 9) {
          // 系统标签
          const systemTagMap = {
            1: { name: '通知', colorClass: 'red' },
            2: { name: '招聘', colorClass: 'red' },
            3: { name: '商机', colorClass: 'red' },
            4: { name: '报价', colorClass: 'red' },
            5: { name: '已更回复', colorClass: 'red' },
            6: { name: 'PI', colorClass: 'red' },
            7: { name: '订单', colorClass: 'red' },
            8: { name: '样品', colorClass: 'red' },
            9: { name: '询盘', colorClass: 'red' }
          };

          const tagInfo = systemTagMap[tagId];
          if (tagInfo) {
            return {
              id: `sys-${tagId}`,
              name: tagInfo.name,
              type: 'system',
              colorClass: tagInfo.colorClass
            };
          }
        } else {
          // 自定义标签
          const tag = this.tags.find(t => t.id === tagId);
          if (tag) {
            return {
              id: `custom-${tag.id}`,
              name: tag.name,
              type: 'custom',
              colorClass: this.getColorClassFromHex(tag.color)
            };
          }
        }
        return null;
      }).filter(Boolean);
    },

    // 从十六进制颜色值获取CSS类名
    getColorClassFromHex(hexColor) {
      const colorMap = {
        '#4caf50': 'green',
        '#9c27b0': 'purple',
        '#795548': 'brown',
        '#00bcd4': 'cyan',
        '#ff9800': 'orange',
        '#e60012': 'red',
        '#2196f3': 'blue'
      };
      return colorMap[hexColor] || 'red';
    },

    // 删除标签
    deleteTag(tag) {
      if (confirm(`确定要删除标签 "${tag.name}" 吗？`)) {
        // 从标签列表中删除
        const index = this.tags.findIndex(t => t.id === tag.id);
        if (index !== -1) {
          this.tags.splice(index, 1);
        }

        // 从所有邮件中移除该标签
        this.emails.forEach(email => {
          if (email.tags && email.tags.includes(tag.id)) {
            email.tags = email.tags.filter(id => id !== tag.id);
          }
        });

        // 如果当前正在筛选该标签，清除筛选
        if (this.activeTag === tag.id) {
          this.clearTagFilter();
        }
      }
    },

    // 根据标签ID获取标签名称
    getTagName(tagId) {
      // 检查是否是系统标签（1-9）
      if (tagId >= 1 && tagId <= 9) {
        const systemTagNames = {
          1: '通知',
          2: '招聘',
          3: '商机',
          4: '报价',
          5: '已更回复',
          6: 'PI',
          7: '订单',
          8: '样品',
          9: '询盘'
        };
        return systemTagNames[tagId] || '';
      } else {
        // 自定义标签
        const tag = this.tags.find(t => t.id === tagId);
        return tag ? tag.name : '';
      }
    },

    // 根据标签ID获取标签颜色
    getTagColor(tagId) {
      // 检查是否是系统标签（1-9）
      if (tagId >= 1 && tagId <= 9) {
        // 系统标签统一使用红色
        return '#e60012';
      } else {
        // 自定义标签
        const tag = this.tags.find(t => t.id === tagId);
        return tag ? tag.color : '#999';
      }
    },

    // 获取标签下的邮件数量
    getTagCount(tagId) {
      return this.emails.filter(email =>
        email.tags && email.tags.includes(tagId)
      ).length;
    },

    // 按标签筛选邮件
    filterByTag(tagId) {
      // 清除其他筛选条件
      this.activeFilter = null;
      this.activeFolder = '';
      this.activeArchiveFolder = null;
      this.searchData.isSearchActive = false;
      this.searchData.term = '';

      // 清除当前选中的邮件
      this.selectedEmail = null;

      // 如果点击当前激活的标签，则取消筛选
      this.activeTag = this.activeTag === tagId ? null : tagId;

      // 如果取消了标签筛选，回到收件箱
      if (!this.activeTag) {
        this.activeFolder = 'inbox';
      }

      // 自动选择第一封邮件
      this.$nextTick(() => {
        this.selectFirstEmail();
      });
    },

    // 清除标签筛选
    clearTagFilter() {
      this.activeTag = null;
      this.activeFolder = 'inbox';
    },

    // 按文件夹筛选
    filterByFolder(folder) {
      this.activeFolder = folder;
      this.activeTag = null;
      this.activeFilter = null;
      this.activeArchiveFolder = null;
      this.searchData.isSearchActive = false;
      this.searchData.term = '';

      // 清除当前选中的邮件
      this.selectedEmail = null;

      // 自动选择第一封邮件
      this.$nextTick(() => {
        this.selectFirstEmail();
      });
    },

    // 为邮件添加标签
    addTagToEmail(email, tagId) {
      if (!email.tags) {
        email.tags = [];
      }

      if (!email.tags.includes(tagId)) {
        email.tags.push(tagId);
      }

      this.showTagSelector = false;
    },

    // 从邮件中移除标签
    removeTagFromEmail(email, tagId) {
      if (email.tags) {
        // 从邮件中移除标签
        email.tags = email.tags.filter(id => id !== tagId);

        // 如果当前有打开的标签管理弹框，也需要从已选标签中移除该标签
        if (this.showTagModal && this.$refs.tagManagement) {
          this.$refs.tagManagement.removeTagById(tagId);
        }

        // 更新当前选中的标签状态
        this.currentSelectedTags = this.getSelectedTagsForModal();

        // 显示提示消息
        this.showToastMessage('标签已移除', 'success');
      }
    },

    // 归档文件夹相关方法
    // 打开归档文件夹管理模态框
    openFolderModal() {
      this.folderForm = {
        id: null,
        name: '',
        color: '#4caf50'
      };
      this.editingFolder = false;
      this.showFolderModal = true;
    },

    // 从归档选项中打开文件夹创建模态框
    openFolderModalFromArchive() {
      this.openFolderModal();
      this.closeArchiveModal();
    },

    // 关闭归档文件夹管理模态框
    closeFolderModal() {
      this.showFolderModal = false;
    },

    // 编辑归档文件夹
    editFolder(folder) {
      this.folderForm = { ...folder };
      this.editingFolder = true;
      this.showFolderModal = true;
    },

    // 保存归档文件夹
    saveFolder() {
      if (!this.folderForm.name.trim()) {
        alert('请输入文件夹名称');
        return;
      }

      if (this.editingFolder) {
        // 更新现有文件夹
        const index = this.archiveFolders.findIndex(f => f.id === this.folderForm.id);
        if (index !== -1) {
          this.archiveFolders.splice(index, 1, { ...this.folderForm });
        }
      } else {
        // 创建新文件夹
        const newId = this.archiveFolders.length > 0
          ? Math.max(...this.archiveFolders.map(f => f.id)) + 1
          : 1;

        this.archiveFolders.push({
          id: newId,
          name: this.folderForm.name,
          color: this.folderForm.color
        });

        // 如果是从归档选项创建的，立即归档到新文件夹
        if (this.emailToArchive) {
          this.archiveEmail(newId);
        }
      }

      this.closeFolderModal();
    },

    // 删除归档文件夹
    deleteFolder(folder) {
      if (confirm(`确定要删除归档文件夹 "${folder.name}" 吗？这将取消所有归档到该文件夹的邮件。`)) {
        // 从文件夹列表中删除
        const index = this.archiveFolders.findIndex(f => f.id === folder.id);
        if (index !== -1) {
          this.archiveFolders.splice(index, 1);
        }

        // 从所有邮件中移除该归档文件夹
        this.emails.forEach(email => {
          if (email.archiveFolder === folder.id) {
            delete email.archiveFolder;
          }
        });

        // 如果当前正在筛选该归档文件夹，清除筛选
        if (this.activeArchiveFolder === folder.id) {
          this.clearArchiveFilter();
        }
      }
    },

    // 根据归档文件夹ID获取文件夹名称
    getArchiveFolderName(folderId) {
      const folder = this.archiveFolders.find(f => f.id === folderId);
      return folder ? folder.name : '';
    },

    // 根据归档文件夹ID获取文件夹颜色
    getArchiveFolderColor(folderId) {
      const folder = this.archiveFolders.find(f => f.id === folderId);
      return folder ? folder.color : '#999';
    },

    // 获取归档文件夹下的邮件数量
    getArchiveFolderCount(folderId) {
      return this.emails.filter(email => email.archiveFolder === folderId).length;
    },

    // 按归档文件夹筛选邮件
    filterByArchiveFolder(folder) {
      // 清除其他筛选条件
      this.activeFilter = null;
      this.activeFolder = '';
      this.activeTag = null;
      this.searchData.isSearchActive = false;
      this.searchData.term = '';

      // 清除当前选中的邮件
      this.selectedEmail = null;

      // 如果点击当前激活的归档文件夹，则取消筛选
      this.activeArchiveFolder = this.activeArchiveFolder === folder.id ? null : folder.id;

      // 如果取消了归档文件夹筛选，回到收件箱
      if (!this.activeArchiveFolder) {
        this.activeFolder = 'inbox';
      }

      // 自动选择第一封邮件
      this.$nextTick(() => {
        this.selectFirstEmail();
      });
    },

    // 清除归档文件夹筛选
    clearArchiveFilter() {
      this.activeArchiveFolder = null;
      this.activeFolder = 'inbox';
    },

    // 显示归档选项
    showArchiveOptions(email) {
      this.emailToArchive = email;
      this.showArchiveModal = true;
    },

    // 关闭归档选项模态框
    closeArchiveModal() {
      this.showArchiveModal = false;
      this.emailToArchive = null;
    },

    // 归档邮件
    archiveEmail(folderId) {
      if (this.emailToArchive) {
        // 设置邮件的归档文件夹
        this.emailToArchive.archiveFolder = folderId;

        // 显示成功提示
        this.showToastMessage(`邮件已归档到"${this.getArchiveFolderName(folderId)}"文件夹`, 'success');

        // 关闭归档选项模态框
        this.closeArchiveModal();
      }
    },

    // 从归档中移除
    removeFromArchive(email) {
      if (email.archiveFolder) {
        const folderName = this.getArchiveFolderName(email.archiveFolder);
        delete email.archiveFolder;
        this.showToastMessage(`邮件已从"${folderName}"文件夹中移除`, 'success');
      }
    },

    // 回复邮件
    replyEmail(email) {
      this.composeMode = 'reply';
      this.replyingTo = email;

      // 设置收件人为原邮件的发件人
      this.composeData.to = email.sender + (email.tag ? ` <${email.tag}>` : '');

      // 设置主题，添加"回复："前缀（如果没有的话）
      let subject = email.subject;
      if (!subject.startsWith('回复:') && !subject.startsWith('Re:')) {
        subject = '回复: ' + subject;
      }
      this.composeData.subject = subject;

      // 设置邮件内容，引用原邮件
      const quoteHeader = `
        <p>------------------ 原始邮件 ------------------</p>
        <p>发件人: ${email.sender}${email.tag ? ` <${email.tag}>` : ''}</p>
        <p>发送时间: ${email.fullDate || email.time}</p>
        <p>主题: ${email.subject}</p>
        <p>收件人: ${email.recipients ? email.recipients.join('; ') : ''}</p>
        <p>------------------------------------------</p>
      `;

      this.composeData.content = `<p><br></p><p><br></p>${quoteHeader}<blockquote style="border-left: 2px solid #ccc; padding-left: 10px; color: #666;">${email.body || ''}</blockquote>`;

      // 切换到编辑器视图
      this.currentView = 'compose';
    },

    // 回复全部
    replyAllEmail(email) {
      this.composeMode = 'replyAll';
      this.replyingTo = email;

      // 设置收件人为原邮件的发件人
      this.composeData.to = email.sender + (email.tag ? ` <${email.tag}>` : '');

      // 设置抄送为原邮件的所有收件人（除了自己）
      if (email.recipients && email.recipients.length > 0) {
        // 过滤掉自己的邮箱地址和"抄送:"开头的内容
        const ccRecipients = email.recipients.filter(r =>
          !r.includes('钟秋玲') && !r.startsWith('抄送:')
        );

        // 提取抄送人
        const ccList = email.recipients.find(r => r.startsWith('抄送:'));
        if (ccList) {
          const ccPeople = ccList.replace('抄送:', '').split(',').map(p => p.trim());
          ccRecipients.push(...ccPeople.filter(p => !p.includes('钟秋玲') && p !== '等3人'));
        }

        this.composeData.cc = ccRecipients.join('; ');
      }

      // 设置主题，添加"回复："前缀（如果没有的话）
      let subject = email.subject;
      if (!subject.startsWith('回复:') && !subject.startsWith('Re:')) {
        subject = '回复: ' + subject;
      }
      this.composeData.subject = subject;

      // 设置邮件内容，引用原邮件
      const quoteHeader = `
        <p>------------------ 原始邮件 ------------------</p>
        <p>发件人: ${email.sender}${email.tag ? ` <${email.tag}>` : ''}</p>
        <p>发送时间: ${email.fullDate || email.time}</p>
        <p>主题: ${email.subject}</p>
        <p>收件人: ${email.recipients ? email.recipients.join('; ') : ''}</p>
        <p>------------------------------------------</p>
      `;

      this.composeData.content = `<p><br></p><p><br></p>${quoteHeader}<blockquote style="border-left: 2px solid #ccc; padding-left: 10px; color: #666;">${email.body || ''}</blockquote>`;

      // 切换到编辑器视图
      this.currentView = 'compose';
    },

    // 转发邮件
    forwardEmail(email) {
      this.composeMode = 'forward';
      this.replyingTo = email;

      // 清空收件人
      this.composeData.to = '';

      // 设置主题，添加"转发："前缀（如果没有的话）
      let subject = email.subject;
      if (!subject.startsWith('转发:') && !subject.startsWith('Fwd:')) {
        subject = '转发: ' + subject;
      }
      this.composeData.subject = subject;

      // 复制附件
      if (email.attachments && email.attachments.length > 0) {
        this.composeData.attachments = [...email.attachments];
      }

      // 设置邮件内容，引用原邮件
      const forwardHeader = `
        <p>------------------ 转发邮件 ------------------</p>
        <p>发件人: ${email.sender}${email.tag ? ` <${email.tag}>` : ''}</p>
        <p>发送时间: ${email.fullDate || email.time}</p>
        <p>主题: ${email.subject}</p>
        <p>收件人: ${email.recipients ? email.recipients.join('; ') : ''}</p>
        <p>------------------------------------------</p>
      `;

      this.composeData.content = `<p><br></p><p><br></p>${forwardHeader}<div>${email.body || ''}</div>`;

      // 如果有签名，也包含在转发内容中
      if (email.signature) {
        this.composeData.content += `<div>${email.signature}</div>`;
      }

      // 切换到编辑器视图
      this.currentView = 'compose';
    },

    // 显示提示消息
    showToastMessage(message, type = 'success') {
      this.toastMessage = message;
      this.toastType = type;
      this.showToast = true;

      // 清除之前的定时器
      if (this.toastTimeout) {
        clearTimeout(this.toastTimeout);
      }

      // 设置新的定时器
      this.toastTimeout = setTimeout(() => {
        this.showToast = false;
      }, 3000);
    },

    // 获取预览URL
    getPreviewUrl(attachment) {
      if (attachment.file) {
        return URL.createObjectURL(attachment.file);
      } else {
        // 模拟URL
        return `@/assets/img/examine_head.png?height=400&width=600&text=${encodeURIComponent(attachment.name)}`;
      }
    },

    // 附件相关方法
    // 处理文件上传
    handleFileUpload(event) {
      const files = event.target.files;
      if (!files || files.length === 0) return;

      this.processFiles(files);

      // 清空文件输入，以便可以再次选择相同的文件
      event.target.value = '';
    },

    // 处理文件
    processFiles(files) {
      // 检查总大小
      let currentTotalSize = this.composeData.attachments.reduce((sum, file) => sum + file.size, 0);
      let newFilesTotalSize = Array.from(files).reduce((sum, file) => sum + file.size, 0);

      if (currentTotalSize + newFilesTotalSize > this.maxTotalSize) {
        this.showToastMessage('附件总大小超过限制（50MB）', 'error');
        return;
      }

      // 处理每个文件
      Array.from(files).forEach(file => {
        // 检查文件大小
        if (file.size > this.maxFileSize) {
          this.showToastMessage(`文件 ${file.name} 超过大小限制（25MB）`, 'error');
          return;
        }

        // 检查文件类型
        const fileExt = this.getFileExtension(file.name).toLowerCase();
        if (!this.isAllowedFileType(fileExt)) {
          this.showToastMessage(`不支持的文件类型: ${fileExt}`, 'error');
          return;
        }

        // 创建附件对象
        const attachment = {
          name: file.name,
          size: file.size,
          type: this.getFileType(file.name),
          file: file,
          uploading: true,
          progress: 0
        };

        // 添加到附件列表
        this.composeData.attachments.push(attachment);

        // 模拟上传进度
        this.simulateUploadProgress(this.composeData.attachments.length - 1);
      });
    },

    // 模拟上传进度
    simulateUploadProgress(index) {
      const attachment = this.composeData.attachments[index];
      let progress = 0;

      const interval = setInterval(() => {
        progress += Math.floor(Math.random() * 10) + 5;
        if (progress >= 100) {
          progress = 100;
          clearInterval(interval);

          // 上传完成后更新附件状态
          setTimeout(() => {
            attachment.uploading = false;
            attachment.progress = 100;
            this.showToastMessage(`文件 ${attachment.name} 上传成功`, 'success');
          }, 500);
        }

        attachment.progress = progress;
      }, 200);
    },

    // 移除附件
    removeAttachment(index) {
      this.composeData.attachments.splice(index, 1);
    },

    // 获取文件扩展名
    getFileExtension(filename) {
      return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
    },

    // 判断文件类型
    getFileType(filename) {
      const ext = this.getFileExtension(filename).toLowerCase();

      if (['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'csv', 'json', 'xml', 'html'].includes(ext)) {
        return 'document';
      } else if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext)) {
        return 'image';
      } else if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'].includes(ext)) {
        return 'video';
      } else if (['mp3', 'wav', 'ogg', 'flac', 'aac'].includes(ext)) {
        return 'audio';
      } else if (['zip', 'rar', '7z', 'tar', 'gz'].includes(ext)) {
        return 'archive';
      } else {
        return 'other';
      }
    },

    // 判断是否为文档文件
    isDocumentFile(filename) {
      const ext = this.getFileExtension(filename).toLowerCase();
      return ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'csv', 'json', 'xml', 'html'].includes(ext);
    },

    // 判断是否为图片文件
    isImageFile(filename) {
      const ext = this.getFileExtension(filename).toLowerCase();
      return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext);
    },

    // 判断是否为可预览的文件
    canPreviewFile(filename) {
      const ext = this.getFileExtension(filename).toLowerCase();
      return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'pdf', 'txt'].includes(ext);
    },

    // 判断是否为PDF文件
    isPdfFile(filename) {
      const ext = this.getFileExtension(filename).toLowerCase();
      return ext === 'pdf';
    },

    // 判断是否为文本文件
    isTextFile(filename) {
      const ext = this.getFileExtension(filename).toLowerCase();
      return ['txt', 'csv', 'json', 'xml', 'html'].includes(ext);
    },

    // 判断是否为允许的文件类型
    isAllowedFileType(ext) {
      return this.allowedFileTypes.some(type => type.toLowerCase().includes(ext.toLowerCase()));
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B';

      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));

      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // 获取附件总大小
    getTotalAttachmentsSize() {
      const totalBytes = this.composeData.attachments.reduce((sum, attachment) => sum + attachment.size, 0);
      return this.formatFileSize(totalBytes);
    },

    // 获取预估邮件大小
    getEstimatedEmailSize() {
      // 计算邮件内容大小（粗略估计）
      const contentSize = this.composeData.content ? this.composeData.content.length : 0;
      const subjectSize = this.composeData.subject ? this.composeData.subject.length : 0;
      const recipientsSize = (this.composeData.to + this.composeData.cc + this.composeData.bcc).length;

      // 基本邮件头部大小估计
      const headerSize = 1000;

      // 附件大小
      const attachmentsSize = this.composeData.attachments.reduce((sum, attachment) => sum + attachment.size, 0);

      // 总大小
      const totalSize = contentSize + subjectSize + recipientsSize + headerSize + attachmentsSize;

      return this.formatFileSize(totalSize);
    },

    // 处理删除邮件
    handleDeleteEmail(data) {
      this.deleteConfirmType = data.type || 'normal';

      switch(this.deleteConfirmType) {
        case 'normal':
          this.deleteConfirmMessage = '您选择了1封邮件，确定要删除吗？';
          break;
        case 'permanent':
          this.deleteConfirmMessage = '邮件删除后将无法恢复，确定要彻底删除吗？';
          break;
        case 'trash':
          this.deleteConfirmMessage = '您确定要将该邮件移入垃圾箱吗？';
          break;
      }

      this.showDeleteConfirmDialog = true;
    },

    // 确认删除邮件
    confirmDeleteEmail() {
      if (!this.selectedEmail) return;

      // 根据删除类型执行不同的操作
      switch(this.deleteConfirmType) {
        case 'normal':
          // 普通删除，将邮件标记为已删除
          this.selectedEmail.deleted = true;
          this.showToastMessage('邮件已删除', 'success');
          break;
        case 'permanent':
          // 彻底删除，从数组中移除邮件
          const index = this.emails.findIndex(email => email.id === this.selectedEmail.id);
          if (index !== -1) {
            this.emails.splice(index, 1);
          }
          this.showToastMessage('邮件已彻底删除', 'success');
          break;
        case 'trash':
          // 移入垃圾箱，将邮件标记为垃圾邮件
          this.selectedEmail.trash = true;
          this.showToastMessage('邮件已移入垃圾箱', 'success');
          break;
      }

      // 关闭确认对话框
      this.showDeleteConfirmDialog = false;

      // 如果当前邮件被删除，选择下一封邮件
      if (this.deleteConfirmType === 'permanent' || this.selectedEmail.deleted || this.selectedEmail.trash) {
        this.selectedEmail = null;

        // 如果有下一封邮件，选择下一封
        if (this.filteredEmails.length > 0) {
          this.selectedEmail = this.filteredEmails[0];
        }
      }
    },

    // 取消删除邮件
    cancelDeleteEmail() {
      this.showDeleteConfirmDialog = false;
    },

    // 处理修改主题
    handleEditSubject(data) {
      if (!data || !data.email || !data.newSubject) return;

      // 更新邮件主题
      data.email.subject = data.newSubject;
      this.showToastMessage('邮件主题已更新', 'success');
    },

    // 处理设置提醒
    handleSetReminder(data) {
      if (!data || !data.email || !data.reminder) return;

      // 创建新的提醒
      const reminder = {
        id: Date.now(),
        emailId: data.email.id,
        time: data.reminder.time,
        note: data.reminder.note,
        completed: false
      };

      // 添加到提醒列表
      this.emailReminders.push(reminder);

      // 显示成功提示
      const reminderTime = new Date(reminder.time);
      const formattedTime = `${reminderTime.toLocaleDateString()} ${reminderTime.toLocaleTimeString()}`;
      this.showToastMessage(`已设置提醒：${formattedTime}`, 'success');
    },

    // 导航到上一封/下一封邮件
    navigateEmail(direction) {
      if (!this.selectedEmail) return;

      const currentIndex = this.filteredEmails.findIndex(email => email.id === this.selectedEmail.id);
      if (currentIndex === -1) return;

      if (direction === 'prev' && this.hasPreviousEmail) {
        this.selectedEmail = this.filteredEmails[currentIndex - 1];
      } else if (direction === 'next' && this.hasNextEmail) {
        this.selectedEmail = this.filteredEmails[currentIndex + 1];
      }
    },

    // 打开全屏查看
    openFullscreenView(email) {
      if (!email) return;

      // 使用全局事件总线添加新标签页
      this.$bus.emit('add-tab', {
        id: `fullscreen-${email.id}`,
        title: `预览: ${email.subject.substring(0, 15)}${email.subject.length > 15 ? '...' : ''}`,
        type: 'fullscreen',
        email: email,
        closable: true,
        isFullscreen: true, // 标记为全屏模式
        keepNavbar: true // 保留顶部导航栏
      });

      // 保留原有功能，以便兼容
      this.selectedEmail = email;
      this.showFullscreenView = true;
    },

    // 关闭全屏查看
    closeFullscreenView() {
      this.showFullscreenView = false;
    },

    // 在新标签页中查看邮件
    viewInNewTab(email) {
      if (!email) return;

      // 使用全局事件总线添加新标签页
      this.$bus.emit('add-tab', {
        id: `fullscreen-${email.id}`,
        title: `查看: ${email.subject.substring(0, 15)}${email.subject.length > 15 ? '...' : ''}`,
        type: 'fullscreen',
        email: email,
        closable: true,
        isFullscreen: true, // 标记为全屏模式
        keepNavbar: true // 保留顶部导航栏
      });
    },

    // 添加新线索
    addNewClue(email) {
      if (!email) return;

      // 使用全局事件总线添加新标签页
      this.$bus.emit('add-tab', {
        id: `clue-${email.id}`,
        title: `新增线索: ${email.subject.substring(0, 10)}${email.subject.length > 10 ? '...' : ''}`,
        type: 'clue',
        email: email,
        closable: true
      });
    },

    // 添加销售订单
    addSalesOrder(email) {
      if (!email) return;

      // 使用全局事件总线添加新标签页
      this.$bus.emit('add-tab', {
        id: `order-${email.id}`,
        title: `新增订单: ${email.subject.substring(0, 10)}${email.subject.length > 10 ? '...' : ''}`,
        type: 'order',
        email: email,
        closable: true
      });
    },

    // 关闭邮件标签页
    closeEmailTab(index) {
      if (index < 0 || index >= this.emailTabs.length) return;

      this.emailTabs.splice(index, 1);

      // 如果关闭的是当前标签页，切换到上一个标签页
      if (this.activeTabIndex >= this.emailTabs.length) {
        this.activeTabIndex = Math.max(0, this.emailTabs.length - 1);
      }
    },

    // 从全屏查看返回邮箱
    returnToInbox() {
      // 清空标签页，关闭全屏查看
      this.emailTabs = [];
    },

    // 预览附件
    previewAttachment(attachment) {
      this.previewingAttachment = attachment;

      if (this.isImageFile(attachment.name)) {
        // 图片预览
        if (attachment.file) {
          // 如果有文件对象（新上传的文件）
          this.previewUrl = URL.createObjectURL(attachment.file);
        } else {
          // 模拟URL（已有的附件）
          this.previewUrl = `./assets/avatar.png`;
        }
      } else if (this.isPdfFile(attachment.name)) {
        // PDF预览
        if (attachment.file) {
          this.previewUrl = URL.createObjectURL(attachment.file);
        } else {
          this.previewUrl = '#';
          this.previewContent = '无法预览此PDF文件';
        }
      } else if (this.isTextFile(attachment.name)) {
        // 文本文件预览
        if (attachment.file) {
          const reader = new FileReader();
          reader.onload = (e) => {
            this.previewContent = e.target.result;
          };
          reader.readAsText(attachment.file);
        } else {
          this.previewContent = '无法预览此文本文件';
        }
      }

      this.showPreviewModal = true;
    },

    // 关闭预览模态框
    closePreviewModal() {
      this.showPreviewModal = false;
      this.previewingAttachment = null;
      this.previewContent = '';

      // 释放对象URL
      if (this.previewUrl && this.previewUrl.startsWith('blob:')) {
        URL.revokeObjectURL(this.previewUrl);
      }
      this.previewUrl = '';
    },

    // 下载附件
    downloadAttachment(attachment) {
      if (attachment.file) {
        // 如果有文件对象（新上传的文件）
        const url = URL.createObjectURL(attachment.file);
        const a = document.createElement('a');
        a.href = url;
        a.download = attachment.name;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      } else {
        // 模拟下载（已有的附件）
        this.showToastMessage(`正在下载: ${attachment.name}`, 'success');
      }
    },

    // 获取星标邮件数量
    getStarredCount() {
      return this.emails.filter(email => email.starred).length;
    },

    // 切换邮件的星标状态
    toggleStar(email) {
      if (email) {
        email.starred = !email.starred;
      }
    },

    // 切换星标置顶状态并显示提示
    toggleStarAndTop(email) {
      if (!email) return;

      // 切换星标状态
      email.starred = !email.starred;

      // 显示提示消息
      if (email.starred) {
        this.showToastMessage('星标置顶成功', 'success');
      } else {
        this.showToastMessage('取消星标置顶成功', 'success');
      }
    },

    // 切换邮件/客户邮件标签页
    switchTab(tab) {
      this.activeTab = tab;

      // 切换标签页时保持中间和右侧区域不变
      // 只是切换左侧的分类区域

      // 如果切换回邮件标签，清除选中的联系人和客户
      if (tab === 'email') {
        this.selectedContact = null;
        this.selectedCustomer = null;
      }
    },

    // 处理联系人选中事件
    handleContactSelected(contact) {
      console.log('Contact selected:', contact);
      this.selectedContact = contact;
      this.selectedCustomer = null; // 清除选中的客户
      // 清除其他筛选条件
      this.activeTag = null;
      this.activeFilter = null;
      this.activeArchiveFolder = null;
      this.searchData.isSearchActive = false;
      this.searchData.term = '';
    },

    // 处理客户选中事件
    handleCustomerSelected(customer) {
      console.log('Customer selected:', customer);
      this.selectedCustomer = customer;
      this.selectedContact = null; // 清除选中的联系人
      // 清除其他筛选条件
      this.activeTag = null;
      this.activeFilter = null;
      this.activeArchiveFolder = null;
      this.searchData.isSearchActive = false;
      this.searchData.term = '';

      // 为客户创建模拟邮件数据（如果还没有）
      if (!customer.emailId) {
        // 为客户分配一个唯一的ID，用于关联邮件
        customer.emailId = `customer-${customer.id}`;

        // 创建与该客户相关的模拟邮件
        const hasAttachment = Math.random() > 0.5;
        const customerEmail = {
          id: 200 + customer.id,
          sender: `${customer.name.toLowerCase().replace(/\s+/g, '.')}@example.com`,
          tag: customer.name,
          time: '2024/11/28 10:30',
          subject: `关于${customer.name}的合作事宜`,
          hasAttachment: hasAttachment,
          hasReply: Math.random() > 0.5,
          recipients: ['钟秋玲', '销售部'],
          ccRecipients: [],
          fullDate: '2024/11/28 10:30',
          body: `<p>尊敬的奔达团队，</p><p>感谢您对我们公司的关注。我们对您的产品非常感兴趣，希望能够建立长期合作关系。</p><p>期待您的回复。</p><p>此致，</p>`,
          signature: `<p><strong>${customer.name}</strong><br>采购经理</p>`,
          tags: [3, 9],
          read: Math.random() > 0.5,
          receivedDate: new Date('2024-11-28T10:30:00'),
          sendDate: new Date('2024-11-28T10:25:00'),
          size: 1200000,
          starred: Math.random() > 0.7,
          folderName: '收件箱',
          account: '<EMAIL>',
          owner: '钟秋玲',
          customerId: customer.id, // 关联到客户ID
          // 如果有附件，添加模拟附件
          attachments: hasAttachment ? [
            { name: 'inquiry_details.pdf', size: 1800000, type: 'document' }
          ] : []
        };

        // 将邮件添加到联系人邮件列表中
        this.contactEmails.push(customerEmail);
      }
    },

    // 处理写邮件事件
    handleComposeEmail(data) {
      console.log('Compose email:', data);
      // 重置邮件编辑器数据
      this.resetComposeData();

      // 设置收件人
      this.composeData.to = data.to;

      // 设置主题（可以根据联系人信息自动生成）
      if (data.contacts && data.contacts.length > 0) {
        const contact = data.contacts[0];
        this.composeData.subject = `关于${contact.company}的合作事宜 - ${contact.name}`;
      } else if (data.customers && data.customers.length > 0) {
        const customerName = data.customers[0].name;
        this.composeData.subject = `关于${customerName}的合作事宜`;
      }

      // 切换到编辑器视图
      this.currentView = 'compose';
    },

    // 切换分类的展开/收起状态
    toggleSection(section) {
      if (this.sections.hasOwnProperty(section)) {
        this.sections[section] = !this.sections[section];
      }
    },

    // 格式化日期时间
    formatDateTime(date) {
      if (!date) return '';

      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      const hours = String(d.getHours()).padStart(2, '0');
      const minutes = String(d.getMinutes()).padStart(2, '0');

      return `${year}/${month}/${day} ${hours}:${minutes}`;
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '';

      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');

      return `${year}/${month}/${day}`;
    },

    // 获取当前活动文件夹名称
    getActiveFolder() {
      if (this.activeFolder === 'inbox') return '收件箱';
      if (this.activeFolder === 'sent') return '发件箱';
      if (this.activeFolder === 'archive') return '已发件箱';
      if (this.activeFolder === 'draft') return '草稿箱';
      if (this.activeFolder === 'recycle') return '回收站';
      if (this.activeFolder === 'spam') return '垃圾邮件箱';

      return '收件箱';
    },

    // 切换抄送人展开/收起状态
    toggleCcExpand() {
      this.showAllCc = !this.showAllCc;
    },

    // 切换语言选择器显示状态
    toggleLanguageSelector() {
      if (!this.selectedEmail) return;

      // 如果已经显示了翻译结果，则关闭翻译并隐藏语言选择栏
      if (this.showTranslation) {
        this.closeTranslation();
        return;
      }

      // 切换语言选择栏的显示状态
      this.showLanguageSelector = !this.showLanguageSelector;
    },

    // 关闭翻译并隐藏语言选择栏
    closeTranslation() {
      this.showTranslation = false;
      this.showLanguageSelector = false;
      this.showOriginalContent = false;
    },

    // 翻译邮件内容 (保留此方法以兼容可能的其他调用)
    translateEmail() {
      this.performTranslation();
    },

    // 选择翻译语言
    selectLanguage(langCode) {
      this.translationLanguage = langCode;

      // 如果已经在显示翻译，则自动更新翻译结果
      if (this.showTranslation) {
        this.performTranslation();
      }
    },

    // 执行翻译
    performTranslation() {
      if (!this.selectedEmail || !this.selectedEmail.body) return;

      this.isTranslating = true;
      this.showOriginalContent = false; // 重置原文显示状态
      this.showTranslation = true; // 显示翻译结果区域
      // 保持语言选择栏可见，不隐藏

      // 模拟翻译API调用
      setTimeout(() => {
        // 这里是模拟翻译结果，实际项目中应该调用翻译API
        const sourceText = this.stripHtml(this.selectedEmail.body);
        let translatedText = '';

        // 根据目标语言生成不同的模拟翻译结果
        if (this.translationLanguage === 'zh') {
          if (sourceText.includes('dear') || sourceText.includes('offer')) {
            translatedText = '<p>亲爱的，</p><p>我们正在处理您的报价。</p><p>我希望能尽快回复您。</p><p>谢谢。</p>';
          } else if (sourceText.includes('Supplier') || sourceText.includes('application')) {
            translatedText = '<p>尊敬的供应商，</p><p>感谢您有兴趣在NORCK GmbH注册。我们已收到您的申请，目前正在审核中。</p><p>请查看附件中的供应商指南和质量要求文档。</p><p>此致，</p>';
          } else {
            translatedText = '<p>这是一个自动生成的翻译结果。</p><p>原文内容已被翻译为中文。</p>';
          }
        } else if (this.translationLanguage === 'en') {
          translatedText = '<p>This is an automatically generated translation.</p><p>The original content has been translated to English.</p>';
        } else {
          translatedText = '<p>This is a simulated translation to ' + this.getLanguageName(this.translationLanguage) + '.</p>';
        }

        this.translatedContent = translatedText;
        this.isTranslating = false;
      }, 1000);
    },

    // 切换原文显示状态
    toggleOriginalView() {
      this.showOriginalContent = !this.showOriginalContent;
    },

    // 处理更多按钮点击
    openMoreOptions() {
      // 这里可以实现更多按钮的功能，例如显示一个下拉菜单
      if (this.selectedEmail) {
        // 可以打开一个包含更多选项的下拉菜单
        this.showToastMessage('更多功能选项', 'success');
      }
    },

    // 删除邮件
    deleteEmail(email) {
      if (!email) return;

      this.selectedEmail = email;
      this.deleteConfirmType = 'normal';
      this.deleteConfirmMessage = `确定要删除邮件 "${email.subject}" 吗？`;
      this.showDeleteConfirmDialog = true;
    },

    // 打开修改主题对话框
    openEditSubjectDialog(email) {
      if (!email) return;

      this.selectedEmail = email;
      this.editedSubject = email.subject;
      this.showEditSubjectDialog = true;
    },

    // 打开提醒设置对话框
    openReminderDialog(email) {
      if (!email) return;

      this.selectedEmail = email;
      this.showReminderDialog = true;
    },

    // 打印邮件
    printEmail(email) {
      if (!email) return;

      this.showToastMessage('正在准备打印...', 'info');

      // 创建打印内容
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        this.showToastMessage('无法打开打印窗口，请检查浏览器设置', 'error');
        return;
      }

      // 构建打印内容
      const printContent = `
        <html>
        <head>
          <title>打印邮件: ${email.subject}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { margin-bottom: 20px; }
            .subject { font-size: 18px; font-weight: bold; margin-bottom: 10px; }
            .meta { margin-bottom: 5px; color: #666; }
            .body { margin-top: 20px; }
            .signature { margin-top: 20px; color: #666; border-top: 1px solid #eee; padding-top: 10px; }
            .attachments { margin-top: 20px; border-top: 1px solid #eee; padding-top: 10px; }
            .attachment { margin: 5px 0; }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="subject">${email.subject}</div>
            <div class="meta">发件人: ${email.sender} ${email.tag ? `<${email.tag}>` : ''}</div>
            <div class="meta">收件人: ${this.formatRecipients(email.recipients)}</div>
            ${email.ccRecipients && email.ccRecipients.length ? `<div class="meta">抄送: ${this.formatRecipients(email.ccRecipients)}</div>` : ''}
            <div class="meta">时间: ${email.fullDate || email.time}</div>
          </div>
          <div class="body">${email.body || ''}</div>
          ${email.signature ? `<div class="signature">${email.signature}</div>` : ''}
          ${email.attachments && email.attachments.length ? `
            <div class="attachments">
              <div>附件 (${email.attachments.length}):</div>
              ${email.attachments.map(att => `<div class="attachment">- ${att.name} (${this.formatFileSize(att.size)})</div>`).join('')}
            </div>
          ` : ''}
        </body>
        </html>
      `;

      printWindow.document.write(printContent);
      printWindow.document.close();

      // 等待图片加载完成后打印
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 500);
    },

    // 获取语言名称
    getLanguageName(langCode) {
      const lang = this.languages.find(l => l.code === langCode);
      return lang ? lang.name : langCode;
    },

    // 移除HTML标签
    stripHtml(html) {
      const tmp = document.createElement('DIV');
      tmp.innerHTML = html;
      return tmp.textContent || tmp.innerText || '';
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (!bytes || bytes === 0) return '0 KB';

      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));

      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // 格式化收件人列表
    formatRecipients(recipients) {
      if (!recipients) return '';
      if (typeof recipients === 'string') return recipients;

      return recipients.join('; ');
    },

    // 显示提示消息
    showToastMessage(message, type = 'info') {
      // 清除之前的定时器
      if (this.toastTimeout) {
        clearTimeout(this.toastTimeout);
      }

      // 设置提示消息
      this.toastMessage = message;
      this.toastType = type;
      this.showToast = true;

      // 3秒后自动关闭
      this.toastTimeout = setTimeout(() => {
        this.showToast = false;
      }, 3000);
    },

    // 高级搜索相关方法
    openAdvancedSearchModal() {
      this.showAdvancedSearchModal = true;
    },

    closeAdvancedSearchModal() {
      this.showAdvancedSearchModal = false;
    },

    handleAdvancedSearch(filters) {
      this.advancedSearchFilters = filters;

      // 清除其他筛选条件
      this.activeTag = null;
      this.activeFolder = '';
      this.activeArchiveFolder = null;
      this.activeFilter = null;
      this.searchData.term = '';

      // 显示提示信息
      this.showToastMessage('已应用高级搜索筛选', 'success');

      // 关闭高级搜索弹框
      this.closeAdvancedSearchModal();
    },

    // AI智能摘要相关方法
    async generateAiSummary() {
      if (!this.selectedEmail) {
        this.showToastMessage('请先选择一封邮件', 'warning');
        return;
      }

      this.isGeneratingSummary = true;
      this.showAiSummary = true;

      try {
        // 调用AI摘要接口
        const response = await this.$http.post('/api/ai/email-summary', {
          emailId: this.selectedEmail.id,
          subject: this.selectedEmail.subject,
          content: this.selectedEmail.body,
          sender: this.selectedEmail.sender
        });

        if (response.data && response.data.success) {
          this.aiSummaryContent = response.data.summary;
        } else {
          throw new Error(response.data?.message || '生成摘要失败');
        }
      } catch (error) {
        console.error('AI摘要生成失败:', error);
        // 使用模拟数据作为后备
        this.aiSummaryContent = this.generateMockSummary();
        this.showToastMessage('AI摘要生成完成（模拟数据）', 'success');
      } finally {
        this.isGeneratingSummary = false;
      }
    },

    // 生成模拟摘要内容
    generateMockSummary() {
      if (!this.selectedEmail) return '';

      const email = this.selectedEmail;
      const summaries = [
        `Katarzyna Gorska正在询问轮子模具的相关信息，包括价格、交货时间、运输和最小订单数量。她还要求提供目录，并期待及时回复。这是一个潜在的商业机会，建议优先处理。`,
        `客户对我们的压铸模具制造服务表示兴趣，询问汽车零件铝压铸模具和电子外壳锌压铸模具的报价。这些模具将用于2025年第二季度推出的新产品线。建议及时提供详细报价。`,
        `客户确认订单，包括5套压铸模具和初始设置的技术支持。已附上正式采购订单和付款详情。这是一个已确认的订单，需要安排生产计划。`,
        `客户正在处理我们的报价，希望尽快回复。这表明客户对我们的产品有兴趣，建议主动跟进了解具体需求和时间安排。`
      ];

      // 根据邮件内容选择合适的摘要
      if (email.body && email.body.includes('molds for wheels')) {
        return summaries[0];
      } else if (email.body && email.body.includes('quotation')) {
        return summaries[1];
      } else if (email.body && email.body.includes('confirm our order')) {
        return summaries[2];
      } else {
        return summaries[3];
      }
    },

    // 关闭AI摘要
    closeAiSummary() {
      this.showAiSummary = false;
      this.aiSummaryContent = '';
    }

  }
}
</script>
<style lang="scss" scoped>
.navbar-button {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  background: none;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  color: #333;
  font-size: 14px;
  transition: background-color 0.2s;
}

.navbar-button:hover {
  background-color: #e6e9ed;
}

.navbar-button i {
  margin-right: 4px;
  font-size: 16px;
}

/* 基础样式 */
.email-app {
  display: flex;
  padding:40px 0;
  flex-direction: column;
  height: 100vh;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  color: #333;
}

.email-inbox {
  display: flex;
  height: 100%;
  overflow: hidden;
}

/* 侧边栏样式 */
.sidebar {
  width: 360px;
  // background-color: #f5f7fa;
  border-right: 1px solid #e6e9ed;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  flex-shrink: 0;
}

.user-info {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e6e9ed;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 10px;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.username {
  display: flex;
  align-items: center;
  font-weight: 500;
}

.compose-btn {
  margin: 16px;
  padding: 10px 16px;
  background-color: #0052CC;
  color: white;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sidebar-section {
  margin-bottom: 8px;
}

.section-header {
  padding: 10px 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.section-content {
  padding: 0 8px;
}

.sidebar-item {
  padding: 8px 16px;
  display: flex;
  align-items: center;
  cursor: pointer;
  border-radius: 4px;
  margin: 2px 0;
}

.sidebar-item:hover {
  background-color: #e6f7ff;
}

.sidebar-item.active {
  background-color: #e6f7ff;
  color: #0052CC;
}

.count, .badge, .tag-count, .folder-count {
  margin-left: auto;
  background-color: #f0f0f0;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 12px;
}

.folder-section {
  margin-top: auto;
  padding-bottom: 16px;
}

.folder-item {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.folder-item:hover {
  background-color: #e6f7ff;
}

.folder-item.active {
  background-color: #e6f7ff;
  color: #0052CC;
}

.tag-group {
  margin-bottom: 12px;
}

.tag-group-header {
  display: flex;
  align-items: center;
  padding: 6px 8px;
  font-weight: 500;
  color: #606266;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
}

.tag-group-header:hover {
  background-color: #f5f5f5;
}

.tag-group-header .icon-tiny {
  margin-right: 6px;
}

.tag-group-content {
  padding-left: 12px;
  margin-top: 4px;
}

.tag-item, .folder-item {
  position: relative;
}

.tag-color-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.tag-actions, .folder-actions {
  display: none;
  position: absolute;
  right: 35px;
}

.tag-item:hover .tag-actions,
.folder-item:hover .folder-actions {
  display: flex;
}

.add-tag-btn, .add-folder-btn {
  margin-left: auto;
  background: none;
  border: none;
  cursor: pointer;
  color: #0052CC;
}

/* 邮件列表样式 */
.email-list {
  flex: 0.5;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #e6e9ed;
  overflow: hidden;
}

.email-tabs {
  display: flex;
  border-bottom: 1px solid #e6e9ed;
}

.tab {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  width: 50%;
    text-align: center;
}

.tab.active {
  border-bottom-color: #0052CC;
  color: #0052CC;
  font-weight: 500;
}

.email-filter {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e6e9ed;
}

.filter-left {
  display: flex;
  align-items: center;
  margin-right: 16px;
}

.search-container {
  flex: 1;
  display: flex;
  align-items: center;
}

.search-box {
  flex: 1;
  position: relative;
}

.search-box input {
  width: 100%;
  padding: 8px 32px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

.search-icon {
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
}

.clear-icon {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  cursor: pointer;
}

.advanced-search-btn {
  margin-left: 8px;
  background: none;
  border: none;
  cursor: pointer;
}

.filter-actions {
  margin-left: 16px;
  display: flex;
  align-items: center;
}

.advanced-search {
  padding: 8px 16px;
  border-bottom: 1px solid #e6e9ed;
}

.search-filters {
  display: flex;
}

.filter-option {
  padding: 4px 12px;
  cursor: pointer;
  border-radius: 4px;
  margin-right: 8px;
}

.filter-option.active {
  background-color: #e6f7ff;
  color: #0052CC;
}

.email-group {
  flex: 1;
  overflow-y: auto;
}

.group-header, .search-results-header {
  padding: 8px 16px;
  background-color: #f9f9f9;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.clear-search-btn {
  margin-left: auto;
  background: none;
  border: none;
  color: #0052CC;
  cursor: pointer;
}

.email-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  display: grid;
  grid-template-columns: auto 1fr auto;
  grid-template-rows: auto auto;
  grid-template-areas:
    "actions sender time"
    "actions subject actions-right";
  gap: 4px 12px;
  position: relative;
  transition: background-color 0.2s;
}

.email-item:hover {
  background-color: #f5f7fa;
}

.email-item.unread {
  background-color: #e6f7ff;
  font-weight: 700;
}

.email-item[class*="starred"] {
  background-color: #fffbe6;
}

.email-item[class*="starred"]::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: #f0c14b;
}

.email-actions-container {
  grid-area: actions;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.star-container, .archive-container {
  margin: 4px 0;
  cursor: pointer;
}

.star-icon {
  color: #d9d9d9;
  transition: color 0.2s;
}

.star-icon:hover {
  color: #f0c14b;
}

.star-icon.starred {
  color: #f0c14b;
  fill: #f0c14b;
}

.email-sender {
  grid-area: sender;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.email-time {
  grid-area: time;
  color: #999;
  font-size: 12px;
}

.email-subject {
  grid-area: subject;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Ensure all text elements in unread emails are bold */
.email-item.unread .email-sender,
.email-item.unread .email-subject,
.email-item.unread .email-time {
  font-weight: 700;
}

.email-tags {
  display: inline-flex;
  margin-left: 8px;
}

.email-tag-label {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  color: white;
}

/* 系统标签（灰色背景）使用深色文本 */
.email-tag-label[style*="background-color: #f0f0f0"] {
  color: #0052cc;
}

.email-archive-folder {
  display: inline-flex;
  align-items: center;
  margin-left: 8px;
  font-size: 12px;
  color: #666;
}

.email-actions {
  grid-area: actions-right;
  display: flex;
  align-items: center;
  color: #999;
  font-size: 12px;
}

.no-results {
  padding: 24px;
  text-align: center;
  color: #999;
}

.email-pagination {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  border-top: 1px solid #e6e9ed;
}

.pagination-info {
  margin-right: auto;
  color: #666;
}

.pagination-size {
  margin-right: 16px;
  display: flex;
  align-items: center;
}

.pagination-nav {
  display: flex;
  align-items: center;
}

/* 邮件内容样式 */
.email-content {
  flex: 1.5;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.email-header {
  padding: 16px;
  border-bottom: 1px solid #e6e9ed;
}

.email-title-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.email-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  margin-right: 12px;
}

.email-tags-inline {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
}

.email-actions-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ccc;
  margin-bottom: 16px;
}

.email-action-buttons {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
}

.action-button {
  padding: 6px 12px;
  background-color: #f5f7fa;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  margin-right: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.action-button .starred {
  color: #f0c14b;
}

.email-toolbar {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #e6e9ed;
  // padding-top: 12px;
}

.toolbar-group {
  display: flex;
  margin-left: 16px;
  justify-content: flex-end;
}

.toolbar-btn {
  background: none;
  border: none;
  padding: 4px 8px;
  cursor: pointer;
  color: #666;
}

.toolbar-btn.active-btn {
  color: #0052CC;
}
.email-meta-container{
  background-color: #f4f5f6;
}

.email-meta {
  padding: 6px 16px;
  display: flex;
  justify-content: space-between;
}

.sender-info {
  display: flex;
}

.recipients {
  display: flex;
  flex-wrap: wrap;
  margin-left: 8px;
}

.recipient {
  margin-right: 8px;
}

.email-date {
  color: #999;
}

.email-tags-container {
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #f0f0f0;
}

.email-tags-list {
  display: flex;
  flex-wrap: wrap;
}

.email-tag-label, .email-archive-folder-label {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  margin-bottom: 4px;
  color: white;
}

.email-archive-folder-label {
  background-color: #52c41a;
}

.email-tags-actions {
  position: relative;
}

.add-tag-to-email {
  background: none;
  border: 1px solid #d9d9d9;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.tag-selector {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;
  min-width: 150px;
}

.tag-option {
  padding: 8px 12px;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.tag-option:hover {
  background-color: #f5f7fa;
}

.tag-option.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.tag-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 8px;
}

.no-tags-option {
  padding: 8px 12px;
  color: #999;
}

.email-attachments {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.attachments-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 500;
}

.attachments-list {
  display: flex;
  flex-wrap: wrap;
}

.attachment-item {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 8px;
  margin-right: 12px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.attachment-icon {
  margin-right: 8px;
}

.attachment-info {
  margin-right: 12px;
}

.attachment-name {
  font-weight: 500;
}

.attachment-size {
  font-size: 12px;
  color: #999;
}

.attachment-actions {
  display: flex;
}

.email-body {
  padding: 16px;
  flex: 1;
  line-height: 1.6;
  color: #303133;
}

.email-signature {
  margin-top: 24px;
  padding: 20px;
  color: #606266;
  font-size: 14px;
  line-height: 1.6;
  border-radius: 0 0 4px 4px;
}

.signature-header {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px dashed #e4e7ed;
}

.signature-content {
  padding: 0 8px;
}

.email-signature hr {
  margin: 16px 0;
  border: none;
  border-top: 1px solid #ebeef5;
}

.email-signature p {
  margin: 8px 0;
}

.email-signature strong {
  color: #303133;
  font-weight: 600;
}

.email-signature a {
  color: #409eff;
  text-decoration: none;
}

.email-signature a:hover {
  text-decoration: underline;
}

.email-signature div[style*="display: flex"] {
  gap: 16px;
  align-items: flex-start;
  margin-top: 16px;
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.email-signature img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 图标样式 */
.icon-small {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.icon-tiny {
  width: 12px;
  height: 12px;
  margin: 0 2px;
  cursor: pointer;
}

.icon {
  width: 18px;
  height: 18px;
}

/* 确保SVG图标正确显示 */
>>> svg {
  width: inherit;
  height: inherit;
  display: inline-block;
  flex-shrink: 0;
  color: inherit;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(3px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  width: 450px;
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease-out;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
  border-radius: 8px 8px 0 0;
}

.modal-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.close-modal {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: none;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  color: #909399;
  transition: all 0.2s;
}

.close-modal:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #606266;
}

.modal-body {
  padding: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #606266;
  font-size: 14px;
}

.form-group input[type="text"] {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  color: #606266;
  transition: all 0.2s;
  box-sizing: border-box;
}

.form-group input[type="text"]:focus {
  border-color: #409eff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.form-group input[type="text"]::placeholder {
  color: #c0c4cc;
}

.color-picker {
  display: flex;
  flex-wrap: wrap;
  margin-top: 12px;
  gap: 10px;
}

.color-option {
  width: 28px;
  height: 28px;
  border-radius: 4px;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.2s;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.color-option:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.color-option.active {
  border-color: #fff;
  box-shadow: 0 0 0 2px #1890ff, 0 2px 4px rgba(0, 0, 0, 0.2);
}

.color-option.active::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: white;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
  background-color: #fafafa;
  border-radius: 0 0 8px 8px;
  gap: 12px;
}

.cancel-btn, .save-btn {
  padding: 9px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
  border: none;
}

.cancel-btn {
  background-color: #f4f4f5;
  color: #606266;
  border: 1px solid #dcdfe6;
}

.cancel-btn:hover {
  background-color: #e9e9eb;
  color: #303133;
}

.save-btn {
  background-color: #1890ff;
  color: white;
  border: 1px solid #1890ff;
}

.save-btn:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

/* 归档模态框特殊样式 */
.archive-modal {
  width: 500px;
}

.archive-folders-list {
  margin-top: 16px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.archive-folder-option {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s;
  border-bottom: 1px solid #ebeef5;
}

.archive-folder-option:last-child {
  border-bottom: none;
}

.archive-folder-option:hover {
  background-color: #f5f7fa;
}

.archive-folder-option .icon-small {
  margin-right: 12px;
}

.folder-count {
  margin-left: auto;
  color: #909399;
  font-size: 13px;
}

.archive-folder-option.create-new {
  background-color: #f0f9ff;
  color: #1890ff;
}

.archive-folder-option.create-new:hover {
  background-color: #e6f7ff;
}

/* 附件预览模态框特殊样式 */
.preview-modal-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  width: 700px;
  max-width: 90%;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out;
}

.preview-modal-body {
  padding: 0;
  flex: 1;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: auto;
}

.image-preview {
  padding: 24px;
  text-align: center;
}

.image-preview img {
  max-width: 100%;
  max-height: 70vh;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.pdf-preview {
  width: 100%;
  height: 70vh;
}

.text-preview {
  padding: 24px;
  width: 100%;
  overflow: auto;
}

.text-preview pre {
  background-color: #f8f8f8;
  padding: 16px;
  border-radius: 4px;
  white-space: pre-wrap;
  font-family: monospace;
  margin: 0;
}

.no-preview {
  padding: 40px;
  text-align: center;
  color: #909399;
}

.no-preview .large-icon {
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
  color: #c0c4cc;
}

.preview-modal-footer {
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
}

.download-btn {
  display: flex;
  align-items: center;
  padding: 9px 20px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.download-btn:hover {
  background-color: #40a9ff;
}

/* 标签选择器样式优化 */
.tag-selector {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  z-index: 10;
  min-width: 180px;
  overflow: hidden;
  animation: fadeIn 0.2s ease-out;
}

.tag-option {
  padding: 10px 16px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s;
  border-bottom: 1px solid #f0f0f0;
}

.tag-option:last-child {
  border-bottom: none;
}

.tag-option:hover {
  background-color: #f5f7fa;
}

.tag-option.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #f9f9f9;
}

.tag-color {
  width: 14px;
  height: 14px;
  border-radius: 3px;
  margin-right: 10px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.no-tags-option {
  padding: 12px 16px;
  color: #909399;
  text-align: center;
  font-style: italic;
}

/* 标签选择器样式优化 */

/* 响应式调整 */
@media (max-width: 1200px) {
  .email-content {
    flex: 1;
  }
}

@media (max-width: 992px) {
  .email-inbox {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
    max-height: 300px;
  }

  .email-list, .email-content {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .email-item {
    grid-template-columns: auto 1fr;
    grid-template-areas:
      "actions sender time"
      "actions subject subject";
  }

  .email-actions-bar {
    flex-direction: column;
  }

  .email-action-buttons {
    margin-bottom: 8px;
  }

  .language-selector-inline {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .language-selects {
    width: 100%;
  }

  .translation-actions-inline {
    margin-left: 0;
    width: 100%;
    justify-content: flex-end;
  }

  .detail-group {
    flex-direction: column;
  }

  .detail-item {
    width: 100%;
    margin-bottom: 8px;
  }

  .detail-label {
    min-width: 70px;
  }
}

@media (max-width: 480px) {
  .source-lang, .target-lang {
    min-width: 80px;
    max-width: 120px;
  }

  .language-selects {
    flex-wrap: wrap;
    gap: 8px;
  }

  .arrow-icon {
    display: none;
  }

  .detail-label {
    min-width: 65px;
    font-size: 13px;
  }

  .detail-value {
    font-size: 13px;
  }

  .email-details-section {
    padding: 8px 0;
  }

  .detail-group {
    margin-bottom: 6px;
  }
}

.no-data-container{
  text-align: center;
  padding: 20px 0;
   .no-data {
      margin-top: 15%;
    }

    .no-data-name {
      margin-top: 8px;
      font-size: 12px;
      color: $--color-text-regular;
    }
}

/* 提示消息样式 */
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

.toast {
  padding: 12px 16px;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  min-width: 250px;
  max-width: 350px;
  font-size: 14px;
  color: white;
}

.toast.success {
  background-color: #52c41a;
}

.toast.error {
  background-color: #f5222d;
}

.toast .icon-small {
  margin-right: 8px;
}

/* 全屏查看相关样式 */
.fullscreen-tabs-container {
  position: fixed;
  top: 56px; /* 顶部导航栏的高度 */
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  z-index: 900;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
}

/* 全屏邮件容器样式 */
.fullscreen-container {
  position: fixed;
  top: 56px; /* 顶部导航栏的高度 */
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  z-index: 900;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
}



/* 邮件详情展示样式 */
.email-details-section {
  background-color: #f4f5f6;
  border-radius: 4px;
  margin: 8px 0;
  padding: 0;
}

.email-details-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  font-size: 14px;
  color: #606266;
  padding: 0 16px;
}

.detail-group {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.detail-item {
  width: 48%;
  display: flex;
  align-items: flex-start;
}

.detail-label {
  min-width: 40px;
  color: #909399;
  font-weight: 500;
}

.detail-value {
  flex: 1;
  word-break: break-word;
}

/* 翻译功能相关样式 */
.language-selector {
  margin-bottom: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #f9fafc;
  overflow: hidden;
}

.language-selector-inline {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  flex-wrap: wrap;
}

.language-label {
  font-weight: 500;
  margin-right: 12px;
  color: #606266;
}

.language-selects {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 300px;
}

.source-lang, .target-lang {
  padding: 6px 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: white;
  font-size: 14px;
  color: #606266;
  min-width: 100px;
}

.arrow-icon {
  margin: 0 8px;
  color: #909399;
  font-weight: bold;
}

.translation-actions-inline {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 16px;
}

.translate-btn, .close-selector-btn, .close-translation {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.translate-btn {
  background-color: #409eff;
  color: white;
}

.translate-btn:hover {
  background-color: #66b1ff;
}

.close-selector-btn {
  background-color: #909399;
  color: white;
}

.close-selector-btn:hover {
  background-color: #a6a9ad;
}

.close-translation {
  background-color: #f56c6c;
  color: white;
  margin-left: 8px;
  padding: 4px 8px;
  font-size: 12px;
}

.close-translation:hover {
  background-color: #f78989;
}

.email-content-wrapper {
  position: relative;
}

.original-content {
  width: 100%;
}

.translation-result {
  width: 100%;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  background-color: white;
}

.translation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #ecf5ff;
  border-bottom: 1px solid #d9ecff;
  color: #409eff;
  font-weight: 500;
}

.translation-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.view-original {
  background: none;
  border: 1px solid #409eff;
  color: #409eff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.view-original:hover {
  background-color: #ecf5ff;
}

.translation-content {
  padding: 16px;
  background-color: white;
}

.translation-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  color: #909399;
}

.translated-text {
  font-size: 14px;
  line-height: 1.6;
  color: #303133;
}

.original-content-preview {
  margin-top: 24px;
  padding: 20px;
  border-top: 1px dashed #e4e7ed;
  background-color: #f9fafc;
  border-radius: 4px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.05);
}

.original-content-header {
  font-weight: 600;
  margin-bottom: 16px;
  color: #303133;
  font-size: 16px;
  padding-bottom: 12px;
  border-bottom: 1px dashed #e4e7ed;
}

.original-content-preview .email-signature {
  margin-top: 20px;
  background-color: #fff;
}

.close-icon {
  cursor: pointer;
  color: #909399;
  transition: color 0.2s;
}

.close-icon:hover {
  color: #606266;
}

.spin {
  animation: spin 1.5s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* AI智能摘要样式 */
.ai-summary-section {
  padding: 0 20px;
}

.ai-summary-btn {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.ai-summary-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.ai-summary-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.ai-summary-btn .icon-small {
  margin-right: 8px;
}

.ai-summary-result {
  padding: 0 20px;
}

.summary-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 6px 6px 0 0;
  font-weight: 500;
}

.summary-title {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.summary-title .icon-small {
  margin-right: 8px;
}

.close-summary-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-summary-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.summary-content {
  padding: 16px;
  background-color: #f8f9ff;
  border: 1px solid #e1e6ff;
  border-top: none;
  border-radius: 0 0 6px 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.summary-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  color: #667eea;
  font-size: 14px;
}

.summary-loading .icon-small {
  margin-right: 8px;
}

.summary-text {
  font-size: 14px;
  line-height: 1.6;
  color: #303133;
  background-color: white;
  padding: 16px;
  border-radius: 4px;
  border-left: 4px solid #667eea;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}


</style>